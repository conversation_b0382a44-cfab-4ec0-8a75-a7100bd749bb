#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from teacher_dashboard.models import Assessment, Question, StudentResponse
from users.models import User

def check_assessment6():
    print("=== 检查考核6的详细信息 ===")
    
    try:
        assessment = Assessment.objects.get(id=6)
        print(f"考核标题: {assessment.title}")
        print(f"是否发布: {assessment.is_published}")
        
        questions = assessment.questions.all()
        print(f"题目数量: {questions.count()}")
        
        for i, q in enumerate(questions, 1):
            print(f"\n题目 {i} (ID: {q.id}):")
            print(f"  类型: {q.question_type}")
            print(f"  题目: {q.question_text}")
            print(f"  选项: {q.options}")
            print(f"  正确答案: '{q.answer}'")
        
        # 检查学生
        student = User.objects.filter(role='student').first()
        if student:
            print(f"\n学生: {student.username}")
            
            # 检查现有回答
            responses = StudentResponse.objects.filter(student=student, assessment=assessment)
            print(f"现有回答数量: {responses.count()}")
            
            for resp in responses:
                print(f"  - 题目{resp.question.id}: '{resp.answer_text}' (正确: {resp.is_correct})")
        
    except Assessment.DoesNotExist:
        print("❌ 考核6不存在")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == '__main__':
    check_assessment6()

#!/usr/bin/env python
"""
诊断AI批改功能的完整脚本
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.conf import settings
from openai import OpenAI

def test_ai_connection():
    """测试AI连接"""
    print("=== 1. 测试AI连接 ===")
    
    # 检查API密钥
    api_key = getattr(settings, 'DASHSCOPE_API_KEY', None)
    if not api_key:
        print("❌ 没有找到DASHSCOPE_API_KEY")
        return False
    
    print(f"✅ API密钥存在: {api_key[:10]}...")
    
    try:
        # 初始化客户端
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        print("✅ AI客户端初始化成功")
        
        # 测试简单请求
        completion = client.chat.completions.create(
            model="qwen-turbo",
            messages=[
                {"role": "user", "content": "请回答：1+1等于几？"}
            ],
            max_tokens=50
        )
        
        response = completion.choices[0].message.content
        print(f"✅ AI响应成功: {response}")
        return True
        
    except Exception as e:
        print(f"❌ AI连接失败: {e}")
        return False

def test_generate_explanation():
    """测试AI解析生成函数"""
    print("\n=== 2. 测试AI解析生成函数 ===")
    
    try:
        from student_dashboard.views import generate_ai_explanation
        
        # 创建测试题目
        class TestQuestion:
            def __init__(self):
                self.question_text = "什么是Python编程语言？"
                self.answer = "Python是一种高级编程语言"
                self.options = None
        
        test_question = TestQuestion()
        student_answer = "不知道"
        
        print(f"题目: {test_question.question_text}")
        print(f"正确答案: {test_question.answer}")
        print(f"学生答案: {student_answer}")
        
        # 调用AI解析生成
        explanation = generate_ai_explanation(test_question, student_answer)
        
        if explanation:
            print(f"✅ AI解析生成成功:")
            print(f"解析内容: {explanation}")
            return True
        else:
            print("❌ AI解析生成失败 - 返回空值")
            return False
            
    except Exception as e:
        print(f"❌ AI解析生成异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assessment_flow():
    """测试完整的考核流程"""
    print("\n=== 3. 测试完整考核流程 ===")
    
    try:
        from teacher_dashboard.models import Assessment, Question, StudentResponse
        from users.models import User
        
        # 获取测试数据
        student = User.objects.filter(role='student').first()
        assessment = Assessment.objects.filter(is_published=True).first()
        
        if not student:
            print("❌ 没有找到学生用户")
            return False
            
        if not assessment:
            print("❌ 没有找到已发布的考核")
            return False
            
        print(f"✅ 学生: {student.username}")
        print(f"✅ 考核: {assessment.title}")
        
        questions = assessment.questions.all()
        if not questions.exists():
            print("❌ 考核没有题目")
            return False
            
        print(f"✅ 题目数量: {questions.count()}")
        
        # 清除现有回答
        StudentResponse.objects.filter(student=student, assessment=assessment).delete()
        print("✅ 清除现有回答")
        
        # 创建一个错误答案
        first_question = questions.first()
        wrong_answer = "这是一个错误答案"
        
        response = StudentResponse.objects.create(
            student=student,
            assessment=assessment,
            question=first_question,
            answer_text=wrong_answer,
            is_correct=False
        )
        print(f"✅ 创建错误答案: {wrong_answer}")
        
        # 测试AI解析生成
        from student_dashboard.views import generate_ai_explanation
        explanation = generate_ai_explanation(first_question, wrong_answer)
        
        if explanation:
            print(f"✅ 为错误答案生成AI解析成功:")
            print(f"解析: {explanation[:100]}...")
            return True
        else:
            print("❌ AI解析生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试考核流程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface():
    """测试Web界面"""
    print("\n=== 4. 测试Web界面 ===")
    
    try:
        from django.test import Client
        from users.models import User
        
        # 获取学生用户
        student = User.objects.filter(role='student').first()
        if not student:
            print("❌ 没有找到学生用户")
            return False
        
        # 创建测试客户端
        client = Client()
        
        # 尝试登录
        login_success = False
        passwords = ['password123', 'password', '123456', 'admin123']
        
        for pwd in passwords:
            if client.login(username=student.username, password=pwd):
                login_success = True
                print(f"✅ 学生登录成功，密码: {pwd}")
                break
        
        if not login_success:
            print("❌ 学生登录失败")
            return False
        
        # 测试AI测试页面
        response = client.get('/student-dashboard/test-ai-grading/')
        if response.status_code == 200:
            print("✅ AI测试页面访问成功")
            
            # 测试POST请求
            post_data = {
                'question_text': '什么是Python？',
                'correct_answer': 'Python是编程语言',
                'student_answer': '不知道'
            }
            
            post_response = client.post('/student-dashboard/test-ai-grading/', post_data)
            if post_response.status_code == 200:
                content = post_response.content.decode('utf-8')
                if 'AI智能解析' in content:
                    print("✅ AI解析在Web界面中正常显示")
                    return True
                else:
                    print("❌ AI解析未在Web界面中显示")
                    return False
            else:
                print(f"❌ POST请求失败，状态码: {post_response.status_code}")
                return False
        else:
            print(f"❌ AI测试页面访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 AI批改功能完整诊断开始")
    print("=" * 50)
    
    results = []
    
    # 测试1: AI连接
    results.append(("AI连接", test_ai_connection()))
    
    # 测试2: AI解析生成函数
    results.append(("AI解析生成函数", test_generate_explanation()))
    
    # 测试3: 完整考核流程
    results.append(("完整考核流程", test_assessment_flow()))
    
    # 测试4: Web界面
    results.append(("Web界面", test_web_interface()))
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 诊断结果总结:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！AI批改功能正常工作。")
    else:
        print("\n⚠️  部分测试失败，请检查上述错误信息。")

if __name__ == '__main__':
    main()

#!/usr/bin/env python
"""
测试考核提交和AI批改流程
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from teacher_dashboard.models import Assessment, Question, StudentResponse
from django.urls import reverse

User = get_user_model()

def test_assessment_flow():
    print("=== 测试考核提交和AI批改流程 ===")
    
    # 1. 获取测试用户
    try:
        student = User.objects.filter(role='student').first()
        if not student:
            print("❌ 没有找到学生用户")
            return
        print(f"✅ 找到学生: {student.username}")
    except Exception as e:
        print(f"❌ 获取学生用户失败: {e}")
        return
    
    # 2. 获取测试考核
    try:
        assessment = Assessment.objects.filter(is_published=True).first()
        if not assessment:
            print("❌ 没有找到已发布的考核")
            return
        print(f"✅ 找到考核: {assessment.title}")
        print(f"   题目数量: {assessment.questions.count()}")
    except Exception as e:
        print(f"❌ 获取考核失败: {e}")
        return
    
    # 3. 清除现有回答
    try:
        existing_count = StudentResponse.objects.filter(student=student, assessment=assessment).count()
        StudentResponse.objects.filter(student=student, assessment=assessment).delete()
        print(f"✅ 清除了 {existing_count} 条现有回答")
    except Exception as e:
        print(f"❌ 清除现有回答失败: {e}")
        return
    
    # 4. 创建测试客户端并登录
    client = Client()
    # 尝试不同的密码
    passwords = ['password123', 'password', '123456', 'admin123']
    login_success = False

    for pwd in passwords:
        if client.login(username=student.username, password=pwd):
            login_success = True
            print(f"✅ 学生登录成功，密码: {pwd}")
            break

    if not login_success:
        print("❌ 学生登录失败，尝试了所有密码")
        print(f"   用户名: {student.username}")
        return
    
    # 5. 准备提交数据
    questions = assessment.questions.all()
    post_data = {'csrfmiddlewaretoken': 'test'}
    
    for i, question in enumerate(questions):
        # 故意提交一些错误答案来测试AI批改
        if i == 0:
            # 第一题故意答错
            post_data[f'question_{question.id}'] = '错误答案'
        elif question.question_type in ['MC', '选择题']:
            # 选择题答对
            post_data[f'question_{question.id}'] = question.answer
        else:
            # 其他题目答对
            post_data[f'question_{question.id}'] = question.answer
    
    print(f"✅ 准备提交数据: {len(post_data)-1} 个答案")
    
    # 6. 提交考核
    try:
        url = reverse('student_dashboard:assessment_detail', args=[assessment.id])
        response = client.post(url, post_data)
        print(f"✅ 提交响应状态码: {response.status_code}")
        
        if response.status_code == 302:
            print(f"✅ 重定向到: {response.url}")
        else:
            print(f"❌ 未重定向，响应内容: {response.content[:200]}")
            
    except Exception as e:
        print(f"❌ 提交失败: {e}")
        return
    
    # 7. 检查保存的回答
    try:
        saved_responses = StudentResponse.objects.filter(student=student, assessment=assessment)
        print(f"✅ 保存的回答数量: {saved_responses.count()}")
        
        for resp in saved_responses:
            print(f"   - 题目 {resp.question.id}: '{resp.answer_text}' (正确: {resp.is_correct})")
    except Exception as e:
        print(f"❌ 检查保存的回答失败: {e}")
        return
    
    # 8. 测试结果页面
    try:
        result_url = reverse('student_dashboard:assessment_result', args=[assessment.id])
        result_response = client.get(result_url)
        print(f"✅ 结果页面状态码: {result_response.status_code}")
        
        if result_response.status_code == 200:
            content = result_response.content.decode('utf-8')
            if 'AI智能解析' in content:
                print("✅ 结果页面包含AI解析")
            else:
                print("⚠️  结果页面不包含AI解析")
        else:
            print(f"❌ 结果页面访问失败")
            
    except Exception as e:
        print(f"❌ 测试结果页面失败: {e}")
        return
    
    print("=== 测试完成 ===")

if __name__ == '__main__':
    test_assessment_flow()

#!/usr/bin/env python
"""
测试AI批改功能
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from teacher_dashboard.models import Assessment, Question, StudentResponse
from users.models import User
from student_dashboard.views import generate_ai_explanation, _compare_answers

def test_ai_grading():
    print("=== 测试AI批改功能 ===")
    
    # 1. 获取测试数据
    try:
        student = User.objects.filter(role='student').first()
        assessment = Assessment.objects.filter(is_published=True).first()
        
        if not student or not assessment:
            print("❌ 缺少测试数据")
            return
            
        print(f"✅ 学生: {student.username}")
        print(f"✅ 考核: {assessment.title}")
        
        questions = assessment.questions.all()
        print(f"✅ 题目数量: {questions.count()}")
        
    except Exception as e:
        print(f"❌ 获取测试数据失败: {e}")
        return
    
    # 2. 清除现有回答
    try:
        StudentResponse.objects.filter(student=student, assessment=assessment).delete()
        print("✅ 清除现有回答")
    except Exception as e:
        print(f"❌ 清除回答失败: {e}")
        return
    
    # 3. 创建测试回答（故意答错一些）
    try:
        for i, question in enumerate(questions):
            if i == 0:
                # 第一题故意答错
                wrong_answer = "这是一个错误答案"
                StudentResponse.objects.create(
                    student=student,
                    assessment=assessment,
                    question=question,
                    answer_text=wrong_answer,
                    is_correct=False
                )
                print(f"✅ 创建错误答案: 题目{question.id} -> '{wrong_answer}'")
            else:
                # 其他题目答对
                correct_answer = question.answer
                StudentResponse.objects.create(
                    student=student,
                    assessment=assessment,
                    question=question,
                    answer_text=correct_answer,
                    is_correct=True
                )
                print(f"✅ 创建正确答案: 题目{question.id} -> '{correct_answer}'")
                
    except Exception as e:
        print(f"❌ 创建测试回答失败: {e}")
        return
    
    # 4. 测试答案比较功能
    print("\n=== 测试答案比较功能 ===")
    for question in questions:
        response = StudentResponse.objects.filter(student=student, question=question).first()
        if response:
            is_correct = _compare_answers(response.answer_text, question)
            print(f"题目{question.id}: '{response.answer_text}' vs '{question.answer}' -> {'正确' if is_correct else '错误'}")
    
    # 5. 测试AI解析生成
    print("\n=== 测试AI解析生成 ===")
    wrong_response = StudentResponse.objects.filter(student=student, assessment=assessment, is_correct=False).first()
    
    if wrong_response:
        print(f"为错误答案生成AI解析...")
        print(f"题目: {wrong_response.question.question_text}")
        print(f"学生答案: {wrong_response.answer_text}")
        print(f"正确答案: {wrong_response.question.answer}")
        
        try:
            explanation = generate_ai_explanation(wrong_response.question, wrong_response.answer_text)
            if explanation:
                print(f"✅ AI解析生成成功:")
                print(f"解析内容: {explanation}")
            else:
                print("❌ AI解析生成失败")
        except Exception as e:
            print(f"❌ AI解析生成异常: {e}")
    else:
        print("没有找到错误答案，跳过AI解析测试")
    
    print("\n=== 测试完成 ===")

def test_simple_ai():
    """简单测试AI连接"""
    print("=== 简单测试AI连接 ===")
    
    try:
        from openai import OpenAI
        from django.conf import settings
        
        client = OpenAI(
            api_key=settings.DASHSCOPE_API_KEY,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        
        completion = client.chat.completions.create(
            model="qwen-turbo",
            messages=[
                {"role": "user", "content": "请简单回答：1+1等于几？"}
            ],
            max_tokens=50
        )
        
        response = completion.choices[0].message.content
        print(f"✅ AI连接成功，回答: {response}")
        
    except Exception as e:
        print(f"❌ AI连接失败: {e}")

if __name__ == '__main__':
    test_simple_ai()
    print()
    test_ai_grading()

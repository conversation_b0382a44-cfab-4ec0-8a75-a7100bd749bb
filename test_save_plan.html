<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试保存教案功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-area {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3730a3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .content-area {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            min-height: 200px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>测试保存教案功能</h1>
    
    <div class="test-area">
        <h3>1. 模拟生成教案内容</h3>
        <button onclick="generateTestContent()">生成测试教案内容</button>
        <div id="content-display" class="content-area">
            点击上方按钮生成测试内容...
        </div>
    </div>
    
    <div class="test-area">
        <h3>2. 测试保存功能</h3>
        <button id="save-btn" onclick="testSaveFunction()" disabled>保存教案到教案库</button>
        <p>状态: <span id="save-status">等待生成内容...</span></p>
    </div>
    
    <div class="test-area">
        <h3>3. 调试日志</h3>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <script>
        let generatedPlanContent = '';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function generateTestContent() {
            log('开始生成测试教案内容...');
            
            generatedPlanContent = `# 软件工程基础教案

## 教学目标
- 理解软件工程的基本概念和原理
- 掌握软件开发生命周期的各个阶段
- 学会使用基本的软件工程工具和方法

## 教学重点与难点
**重点：**
- 软件工程的定义和重要性
- 软件开发生命周期模型
- 需求分析和设计方法

**难点：**
- 软件质量保证
- 项目管理和团队协作

## 教学准备
- 课件：软件工程概述PPT
- 工具：UML建模工具
- 案例：实际软件项目案例

## 教学过程

### 1. 导入环节（10分钟）
- 回顾上节课内容
- 引入软件工程概念
- 展示软件失败案例

### 2. 新课讲授（25分钟）
- 软件工程定义和特点
- 软件危机和解决方案
- 软件开发生命周期模型

### 3. 互动环节（10分钟）
- 学生讨论：身边的软件工程实例
- 小组分享和点评

### 4. 课堂练习（10分钟）
- 绘制简单的软件开发流程图
- 分析给定案例的需求

### 5. 总结（5分钟）
- 回顾本节课重点内容
- 布置课后作业
- 预告下节课内容

## 课后作业
1. 阅读教材第1-2章
2. 调研一个开源软件项目的开发过程
3. 准备下节课的案例分析

## 教学反思
本节课通过理论讲解和实践练习相结合的方式，帮助学生理解软件工程的基本概念。需要注意的是，要多结合实际案例，让抽象的概念更加具体化。`;

            document.getElementById('content-display').innerHTML = `<pre>${generatedPlanContent}</pre>`;
            document.getElementById('save-btn').disabled = false;
            document.getElementById('save-status').textContent = '内容已生成，可以保存';
            
            log('测试教案内容生成完成');
            log(`内容长度: ${generatedPlanContent.length} 字符`);
        }
        
        function testSaveFunction() {
            log('开始测试保存功能...');
            
            if (!generatedPlanContent || !generatedPlanContent.trim()) {
                log('错误: 没有教案内容');
                alert('请先生成教案内容！');
                return;
            }
            
            // 模拟用户输入标题
            const title = prompt('请输入教案标题：', `测试教案_${new Date().toISOString().slice(0, 10)}_${new Date().toTimeString().slice(0, 5).replace(':', '')}`);
            
            if (!title || !title.trim()) {
                log('用户取消保存或未输入标题');
                return;
            }
            
            log(`准备保存教案，标题: ${title}`);
            log(`教案内容长度: ${generatedPlanContent.length}`);
            
            // 禁用按钮
            const saveBtn = document.getElementById('save-btn');
            saveBtn.disabled = true;
            saveBtn.textContent = '保存中...';
            document.getElementById('save-status').textContent = '正在保存...';
            
            // 模拟CSRF令牌（实际应用中需要从页面获取）
            const csrfToken = 'test-csrf-token';
            
            // 发送保存请求
            fetch('/teacher-dashboard/api/save-lesson-plan/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: new URLSearchParams({
                    'title': title.trim(),
                    'content': generatedPlanContent,
                    'csrfmiddlewaretoken': csrfToken
                })
            })
            .then(response => {
                log(`收到服务器响应: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                log(`保存响应数据: ${JSON.stringify(data)}`);
                if (data.status === 'success') {
                    log('保存成功！');
                    alert('教案已成功保存到教案库！\n\n标题：' + title);
                    document.getElementById('save-status').textContent = '保存成功';
                } else {
                    throw new Error(data.message || '保存失败');
                }
            })
            .catch(error => {
                log(`保存失败: ${error.message}`);
                alert('保存失败: ' + error.message);
                document.getElementById('save-status').textContent = '保存失败';
            })
            .finally(() => {
                // 恢复按钮状态
                saveBtn.disabled = false;
                saveBtn.textContent = '保存教案到教案库';
                log('保存操作完成');
            });
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成');
            log('准备测试保存教案功能');
        });
    </script>
</body>
</html>

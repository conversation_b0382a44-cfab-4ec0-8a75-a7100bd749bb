# 教学实训智能体软件

这是一个基于 Django 和 Python 的教学实训智能体软件，旨在通过集成开源 AI 大模型，提升教学效率和学习体验。

## 功能概述

-   **教师端**：备课与设计、考核内容生成、学情数据分析。
-   **学生端**：在线学习助手、实时练习评测助手。
-   **管理端**：用户管理、课件资源管理、大屏概览。

## 技术栈

-   **后端**: Python, Django
-   **前端**: HTML (后续可集成更丰富的 UI 框架)
-   **AI模型**: 基于开源AI大模型 (例如通义大模型，在此示例中为模拟功能)
-   **数据库**: SQLite (默认，可配置为其他数据库)

## 环境设置

### 1. 克隆项目

```bash
git clone <项目仓库地址>
cd <项目目录>
```

### 2. 创建并激活虚拟环境 (推荐)

```bash
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 或 .\venv\Scripts\activate # Windows
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 数据库迁移

```bash
python manage.py makemigrations users teacher_dashboard student_dashboard admin_dashboard
python manage.py migrate
```

### 5. 创建超级用户

用于访问 Django 管理后台，并创建教师、学生和管理员用户。

```bash
python manage.py createsuperuser
```
按照提示输入用户名、邮箱和密码。

### 6. 运行开发服务器

```bash
python manage.py runserver
```

## 访问应用

-   **Django 管理后台**: `http://127.0.0.1:8000/admin/`

    使用超级用户登录，可以创建和管理教师、学生、管理员用户，并设置其角色。

-   **登录页面**: `http://127.0.0.1:8000/login/`

    使用不同角色的用户登录，将自动重定向到各自的仪表板。

-   **教师仪表板**: `http://127.0.0.1:8000/teacher/dashboard/`

    包含备课与设计、考核内容生成、学情数据分析等功能入口。

-   **学生仪表板**: `http://127.0.0.1:8000/student/dashboard/`

    包含在线学习助手、实时练习评测助手等功能入口。

-   **管理员仪表板**: `http://127.0.0.1:8000/admin-dashboard/dashboard/`

    包含用户管理、课件资源管理、大屏概览等功能入口。

## 进一步开发

-   **AI模型集成**: 当前 AI 功能为模拟实现。实际开发中，需要集成通义大模型或其他本地部署的开源大模型，通过 API 调用实现真正的智能备课、题目生成、学习问答和评测。
-   **本地知识库**: 实现本地知识库的存储和检索，确保 AI 生成内容的关联性和准确性。
-   **前端优化**: 使用更现代的前端框架 (如 React, Vue, Angular) 或更丰富的 Django 模板技术来提升用户界面和体验。
-   **用户管理**: 完善用户创建、编辑和删除功能。
-   **课件资源管理**: 实现课件的上传、下载、编辑、删除以及分类管理。
-   **大屏概览**: 集成图表库 (如 Chart.js, ECharts) 来可视化展示学情数据和教学效率指标。 



开始之前需要先配置环境变量：export DASHSCOPE_API_KEY="sk-3e25b8356c734751b15d2039fa420af6"


管理员端：shuyijin syj17771493975
教师端：teacher password123
学生端：szyee syj17771493975



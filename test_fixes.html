<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #4f46e5;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transition: all 0.3s ease;
            cursor: pointer;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
        }
        .upload-area.dragover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }
        .upload-icon {
            font-size: 3rem;
            color: #64748b;
            margin-bottom: 15px;
        }
        .upload-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 8px;
        }
        .upload-hint {
            font-size: 0.9rem;
            color: #94a3b8;
        }
        .file-input {
            display: none;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #3730a3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .content-area {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            min-height: 200px;
            border: 1px solid #e5e7eb;
        }
        .log {
            background: #1f2937;
            color: #10b981;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.info { background: #dbeafe; color: #1e40af; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <h1>🛠️ 功能修复测试页面</h1>
    
    <!-- 测试1: 文档上传显示功能 -->
    <div class="test-section">
        <h2 class="test-title">📁 测试1: 文档上传显示功能</h2>
        <p>测试文档上传后是否正确显示文件信息</p>
        
        <div class="upload-area" onclick="document.getElementById('test-file-input').click()">
            <div class="upload-icon">
                <i class="fas fa-file-upload"></i>
            </div>
            <div class="upload-text">点击上传或拖拽文件到此处</div>
            <div class="upload-hint">支持 .txt, .pdf, .docx 格式，可同时上传多个文件</div>
            <input class="file-input" type="file" id="test-file-input" multiple accept=".txt,.pdf,.docx,.doc">
        </div>
        
        <div id="upload-status" class="status info">等待选择文件...</div>
    </div>
    
    <!-- 测试2: 导出Word功能 -->
    <div class="test-section">
        <h2 class="test-title">📄 测试2: 导出Word功能</h2>
        <p>测试教案内容导出为Word文档功能</p>
        
        <div class="content-area" id="test-content">
            <h3>测试教案内容</h3>
            <p>这是一个测试教案的内容...</p>
        </div>
        
        <button onclick="generateTestContent()">生成测试教案内容</button>
        <button id="export-word-btn" onclick="testExportWord()" disabled>导出Word文档</button>
        
        <div id="export-status" class="status info">请先生成测试内容...</div>
    </div>
    
    <!-- 调试日志 -->
    <div class="test-section">
        <h2 class="test-title">🔍 调试日志</h2>
        <div id="debug-log" class="log">测试页面已加载...\n</div>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <script>
        let testPlanContent = '';
        
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debug-log').textContent = '日志已清除...\n';
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const statusDiv = document.getElementById(elementId);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        // 测试1: 文档上传显示功能
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('test-file-input');
        
        if (uploadArea && fileInput) {
            log('初始化文件上传测试功能');
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.add('dragover');
                log('文件拖拽悬停');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('dragover');
                log('文件拖拽离开');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                log(`文件拖拽放置: ${files.length} 个文件`);
                
                // 验证文件类型
                const allowedTypes = ['.txt', '.pdf', '.docx', '.doc'];
                const validFiles = Array.from(files).filter(file => {
                    const extension = '.' + file.name.split('.').pop().toLowerCase();
                    return allowedTypes.includes(extension);
                });
                
                if (validFiles.length !== files.length) {
                    updateStatus('upload-status', '部分文件格式不支持，只支持 .txt, .pdf, .docx, .doc 格式', 'error');
                    log('部分文件格式不支持');
                }
                
                if (validFiles.length > 0) {
                    // 创建新的FileList
                    const dt = new DataTransfer();
                    validFiles.forEach(file => dt.items.add(file));
                    fileInput.files = dt.files;
                    updateFileDisplay();
                }
            });

            fileInput.addEventListener('change', (e) => {
                log(`文件选择改变: ${e.target.files.length} 个文件`);
                updateFileDisplay();
            });
        }
        
        function updateFileDisplay() {
            const files = fileInput.files;
            const uploadText = uploadArea.querySelector('.upload-text');
            const uploadHint = uploadArea.querySelector('.upload-hint');
            
            if (files.length > 0) {
                const fileNames = Array.from(files).map(file => {
                    const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
                    return `${file.name} (${sizeInMB}MB)`;
                }).join(', ');
                
                uploadText.textContent = `✓ 已选择 ${files.length} 个文件`;
                uploadText.style.color = '#059669';
                uploadHint.textContent = fileNames;
                uploadHint.style.color = '#374151';
                
                uploadArea.style.borderColor = '#059669';
                uploadArea.style.backgroundColor = '#f0fdf4';
                
                updateStatus('upload-status', `成功选择 ${files.length} 个文件`, 'success');
                log(`文件选择更新: ${files.length} 个文件`);
                Array.from(files).forEach((file, index) => {
                    log(`文件 ${index + 1}: ${file.name}, 大小: ${file.size} bytes`);
                });
            } else {
                uploadText.textContent = '点击上传或拖拽文件到此处';
                uploadText.style.color = '#64748b';
                uploadHint.textContent = '支持 .txt, .pdf, .docx 格式，可同时上传多个文件';
                uploadHint.style.color = '#94a3b8';
                
                uploadArea.style.borderColor = '#cbd5e1';
                uploadArea.style.backgroundColor = '';
                
                updateStatus('upload-status', '等待选择文件...', 'info');
                log('文件选择已清空');
            }
        }
        
        // 测试2: 导出Word功能
        function generateTestContent() {
            log('生成测试教案内容');
            
            testPlanContent = `# 软件工程基础教案

## 教学目标
- 理解软件工程的基本概念和原理
- 掌握软件开发生命周期的各个阶段
- 学会使用基本的软件工程工具和方法

## 教学重点与难点
**重点：**
- 软件工程的定义和重要性
- 软件开发生命周期模型
- 需求分析和设计方法

**难点：**
- 软件质量保证
- 项目管理和团队协作

## 教学过程

### 1. 导入环节（10分钟）
- 回顾上节课内容
- 引入软件工程概念
- 展示软件失败案例

### 2. 新课讲授（25分钟）
- 软件工程定义和特点
- 软件危机和解决方案
- 软件开发生命周期模型

### 3. 互动环节（10分钟）
- 学生讨论：身边的软件工程实例
- 小组分享和点评

### 4. 课堂练习（10分钟）
- 绘制简单的软件开发流程图
- 分析给定案例的需求

### 5. 总结（5分钟）
- 回顾本节课重点内容
- 布置课后作业
- 预告下节课内容

## 课后作业
1. 阅读教材第1-2章
2. 调研一个开源软件项目的开发过程
3. 准备下节课的案例分析

## 教学反思
本节课通过理论讲解和实践练习相结合的方式，帮助学生理解软件工程的基本概念。`;

            document.getElementById('test-content').innerHTML = `<pre>${testPlanContent}</pre>`;
            document.getElementById('export-word-btn').disabled = false;
            updateStatus('export-status', '测试内容已生成，可以导出', 'success');
            log(`测试教案内容生成完成，长度: ${testPlanContent.length} 字符`);
        }
        
        function testExportWord() {
            log('开始测试导出Word功能');
            
            if (!testPlanContent || !testPlanContent.trim()) {
                updateStatus('export-status', '请先生成测试内容', 'error');
                log('错误: 没有测试内容');
                return;
            }
            
            const exportBtn = document.getElementById('export-word-btn');
            exportBtn.disabled = true;
            exportBtn.textContent = '导出中...';
            updateStatus('export-status', '正在导出Word文档...', 'info');
            
            // 模拟导出过程
            setTimeout(() => {
                // 创建一个简单的文本文件作为测试
                const blob = new Blob([testPlanContent], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `测试教案_${new Date().toISOString().slice(0, 10)}.txt`;
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                
                exportBtn.disabled = false;
                exportBtn.textContent = '导出Word文档';
                updateStatus('export-status', '测试导出完成（文本格式）', 'success');
                log('测试导出功能完成');
            }, 2000);
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面初始化完成');
            log('可以开始测试文档上传和导出功能');
        });
    </script>
</body>
</html>

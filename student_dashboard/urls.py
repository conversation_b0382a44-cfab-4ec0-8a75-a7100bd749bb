from django.urls import path
from . import views

app_name = 'student_dashboard'

urlpatterns = [
    path('dashboard/', views.student_dashboard, name='dashboard'),
    path('online_learning_assistant/', views.online_learning_assistant, name='online_learning_assistant'),
    path('realtime_practice_evaluation/', views.realtime_practice_evaluation, name='realtime_practice_evaluation'),
    path('assessments/', views.assessments_content, name='assessments_content'),
    path('learning_assistant/', views.online_learning_assistant, name='learning_assistant_content'),
    path('practice/', views.realtime_practice_evaluation, name='practice_content'),
    path('assessment/<int:assessment_id>/', views.assessment_detail, name='assessment_detail'),
    path('assessment/<int:assessment_id>/result/', views.assessment_result, name='assessment_result'),
    path('master_question/<int:response_id>/', views.master_question, name='master_question'),
    path('courseware_library/', views.courseware_library_view, name='courseware_library'),
    path('test/', views.test_page, name='test_page'),
    path('demo/', views.demo_page, name='demo_page'),
]
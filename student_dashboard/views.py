from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.conf import settings
from openai import OpenAI # Import OpenAI client
from teacher_dashboard.models import Assessment, Question, StudentResponse, Courseware
from django.db import transaction
import re # Added for regex in realtime_practice_evaluation
from django.db.models import F # Added for F in realtime_practice_evaluation
from django.http import JsonResponse
from users.models import User
from django.db.models import Prefetch, Count
from django.views.decorators.http import require_POST

# Create your views here.

@login_required
def student_dashboard(request):
    if request.user.role != "student":
        return redirect("home")
    
    # 该视图只负责渲染仪表盘的骨架
    return render(request, "student_dashboard/dashboard.html")


@login_required
def assessments_content(request):
    """
    该视图为学生仪表盘的iframe提供“我的考核”列表内容。
    """
    if request.user.role != "student":
        return redirect("home") # Or handle error appropriately

    # 获取所有已发布的考核任务
    assessments = Assessment.objects.filter(is_published=True).order_by('-created_at')
    
    # 获取学生已提交答案的考核ID列表
    completed_assessment_ids = StudentResponse.objects.filter(student=request.user).values_list('assessment_id', flat=True).distinct()

    return render(request, "student_dashboard/assessments_content.html", {
        'assessments': assessments,
        'completed_assessment_ids': completed_assessment_ids
    })


@login_required
def assessment_detail(request, assessment_id):
    if request.user.role != "student":
        return redirect("home")

    assessment = get_object_or_404(Assessment, id=assessment_id, is_published=True)

    has_submitted = StudentResponse.objects.filter(student=request.user, assessment=assessment).exists()

    if has_submitted:
        return redirect('student_dashboard:assessment_result', assessment_id=assessment.id)

    if request.method == "POST":
        print(f"=== 开始处理考核提交 ===")
        print(f"学生: {request.user.username}")
        print(f"考核: {assessment.title} (ID: {assessment.id})")

        # 打印所有POST数据（除了CSRF token）
        post_data = {k: v for k, v in request.POST.items() if k != 'csrfmiddlewaretoken'}
        print(f"提交的答案数据: {post_data}")

        try:
            with transaction.atomic():
                # 清除现有回答
                existing_responses = StudentResponse.objects.filter(student=request.user, assessment=assessment)
                existing_count = existing_responses.count()
                existing_responses.delete()
                print(f"✅ 删除了 {existing_count} 条现有记录")

                # 处理新的回答
                submitted_answers = 0
                questions_processed = []

                for key, value in request.POST.items():
                    if key.startswith('question_') and value and value.strip():
                        question_id = key.split('_')[1]
                        try:
                            question = Question.objects.get(id=question_id)
                            answer_text = value.strip()

                            print(f"📝 处理题目 {question_id}")
                            print(f"   题目: {question.question_text[:50]}...")
                            print(f"   学生答案: '{answer_text}'")
                            print(f"   正确答案: '{question.answer}'")

                            # 判断是否正确
                            is_correct = _compare_answers(answer_text, question)
                            print(f"   判断结果: {'✅ 正确' if is_correct else '❌ 错误'}")

                            # 保存回答
                            response = StudentResponse.objects.create(
                                student=request.user,
                                assessment=assessment,
                                question=question,
                                answer_text=answer_text,
                                is_correct=is_correct,
                                is_mastered=is_correct  # 如果答对了就认为掌握了
                            )
                            submitted_answers += 1
                            questions_processed.append(question_id)
                            print(f"   ✅ 保存成功，记录ID: {response.id}")

                        except Question.DoesNotExist:
                            print(f"❌ 警告: 题目 {question_id} 不存在")
                            continue
                        except Exception as e:
                            print(f"❌ 处理题目 {question_id} 时出错: {e}")
                            continue

                print(f"\n=== 提交总结 ===")
                print(f"✅ 总共保存了 {submitted_answers} 个答案")
                print(f"✅ 处理的题目ID: {questions_processed}")

                # 验证是否所有题目都有答案
                total_questions = assessment.questions.count()
                if submitted_answers < total_questions:
                    print(f"⚠️  警告: 只提交了 {submitted_answers}/{total_questions} 个答案")
                else:
                    print(f"✅ 所有 {total_questions} 道题目都已提交")

            # 重定向到结果页面
            print(f"🔄 重定向到结果页面...")
            return redirect('student_dashboard:assessment_result', assessment_id=assessment.id)

        except Exception as e:
            # 详细的错误日志
            import traceback
            error_details = traceback.format_exc()
            print(f"❌ 提交错误详情: {error_details}")
            return render(request, "student_dashboard/assessment_detail.html", {
                'assessment': assessment,
                'error': f'提交时发生错误: {str(e)}'
            })

    return render(request, "student_dashboard/assessment_detail.html", {'assessment': assessment})

def _compare_answers(student_answer, question):
    """增强的答案比较逻辑，支持更智能的判断"""
    if not student_answer or not student_answer.strip():
        return False

    student_answer = student_answer.strip()
    correct_answer = question.answer.strip()

    # 对于选择题，提取选项字母
    if question.question_type in ['MC', '选择题']:
        # 如果学生答案包含选项内容，提取字母部分
        if '.' in student_answer:
            student_answer = student_answer.split('.')[0].strip()
        if '.' in correct_answer:
            correct_answer = correct_answer.split('.')[0].strip()

        # 选择题严格匹配
        return student_answer.upper() == correct_answer.upper()

    elif question.question_type in ['TF', '判断题']:
        # 判断题的智能匹配
        student_normalized = normalize_boolean_answer(student_answer)
        correct_normalized = normalize_boolean_answer(correct_answer)
        return student_normalized == correct_normalized

    else:
        # 主观题的模糊匹配
        return smart_text_compare(student_answer, correct_answer)


def normalize_boolean_answer(answer):
    """标准化判断题答案"""
    answer = answer.lower().strip()

    # 正确的表示
    if any(word in answer for word in ['正确', '对', 'true', 't', '是', 'yes', '√']):
        return 'true'

    # 错误的表示
    if any(word in answer for word in ['错误', '错', 'false', 'f', '否', 'no', '×']):
        return 'false'

    return answer


def smart_text_compare(student_answer, correct_answer, threshold=0.8):
    """智能文本比较，支持模糊匹配"""
    from difflib import SequenceMatcher

    # 基本清理
    student_clean = student_answer.lower().strip()
    correct_clean = correct_answer.lower().strip()

    # 完全匹配
    if student_clean == correct_clean:
        return True

    # 计算相似度
    similarity = SequenceMatcher(None, student_clean, correct_clean).ratio()

    # 如果相似度很高，认为正确
    if similarity >= threshold:
        return True

    # 检查关键词匹配
    student_words = set(student_clean.split())
    correct_words = set(correct_clean.split())

    # 如果关键词重叠度很高，也认为正确
    if len(correct_words) > 0:
        overlap_ratio = len(student_words & correct_words) / len(correct_words)
        if overlap_ratio >= 0.7:
            return True

    return False


@login_required
def assessment_result(request, assessment_id):
    """显示考核结果并生成AI批改"""
    if request.user.role != "student":
        return redirect("home")

    assessment = get_object_or_404(Assessment, id=assessment_id)
    student_responses = StudentResponse.objects.filter(student=request.user, assessment=assessment)

    print(f"=== 开始生成考核结果和AI批改 ===")
    print(f"学生: {request.user.username}")
    print(f"考核: {assessment.title}")
    print(f"学生回答数量: {student_responses.count()}")

    if not student_responses.exists():
        print("❌ 没有找到学生回答，重定向到考核页面")
        return redirect('student_dashboard:assessment_detail', assessment_id=assessment.id)

    results = []
    correct_count = 0
    total_questions = assessment.questions.count()

    # 处理每个题目
    for question in assessment.questions.all():
        response = student_responses.filter(question=question).first()
        is_correct = False
        explanation = None

        if response:
            print(f"\n--- 处理题目 {question.id} ---")
            print(f"题目: {question.question_text[:50]}...")
            print(f"学生答案: '{response.answer_text}'")
            print(f"正确答案: '{question.answer}'")

            # 重新计算是否正确
            is_correct = _compare_answers(response.answer_text, question)
            print(f"判断结果: {'正确' if is_correct else '错误'}")

            # 更新数据库中的正确性标记
            if response.is_correct != is_correct:
                response.is_correct = is_correct
                response.save(update_fields=['is_correct'])
                print(f"已更新数据库中的正确性标记")

            if is_correct:
                correct_count += 1
            else:
                # 为错误答案生成AI解析
                print("开始生成AI解析...")
                explanation = generate_ai_explanation(question, response.answer_text)
                if explanation:
                    print(f"AI解析生成成功: {explanation[:100]}...")
                    # 将AI解析保存到数据库
                    response.explanation = explanation
                    response.save(update_fields=['explanation'])
                    print("AI解析已保存到数据库")
                else:
                    print("AI解析生成失败")
        else:
            print(f"题目 {question.id}: 学生未作答")

        results.append({
            'question': question,
            'student_answer': response.answer_text if response else "未作答",
            'is_correct': is_correct,
            'explanation': explanation
        })

    print(f"\n=== 最终结果 ===")
    print(f"正确题数: {correct_count}/{total_questions}")
    print(f"正确率: {(correct_count/total_questions*100):.1f}%")

    return render(request, 'student_dashboard/assessment_result.html', {
        'assessment': assessment,
        'results': results,
        'correct_count': correct_count,
        'total_questions': total_questions,
    })


def generate_ai_explanation(question, student_answer):
    """增强的AI解析生成函数，提供详细的错误分析和修正建议"""
    try:
        print("初始化AI客户端...")
        client = OpenAI(
            api_key=settings.DASHSCOPE_API_KEY,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        print("AI客户端初始化成功")

        # 构建提示词
        options_text = ""
        if hasattr(question, 'options') and question.options:
            options_text = f"\n选项：\n" + "\n".join([f"  {opt}" for opt in question.options])

        # 增强的提示词，要求更详细的分析
        prompt = f"""请作为专业教师，对学生的答案进行详细的自动化检测和分析：

【题目信息】
题目：{question.question_text}
{options_text}
题目类型：{getattr(question, 'question_type', '未知')}
正确答案：{question.answer}
学生答案：{student_answer}

【分析要求】
请按以下结构提供分析：

1. 【错误定位】：具体指出学生答案的错误之处
2. 【错误原因】：分析为什么会出现这个错误（概念理解、计算错误、审题不清等）
3. 【正确解法】：简要说明正确的解题思路和方法
4. 【修正建议】：给出具体的学习建议和改进方法
5. 【知识点提醒】：相关的重要知识点或注意事项

请用专业但易懂的语言，控制在200字以内，重点突出实用性。"""

        print("发送AI请求...")
        print(f"提示词长度: {len(prompt)} 字符")

        completion = client.chat.completions.create(
            model="qwen-plus",  # 使用更强的模型
            messages=[
                {
                    "role": "system",
                    "content": "你是一位经验丰富的教师和学习诊断专家，擅长精准定位学生的学习问题并提供有效的改进建议。你的分析要具体、实用、有针对性。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0.2,  # 降低随机性，提高一致性
            max_tokens=400
        )

        explanation = completion.choices[0].message.content.strip()
        print(f"AI解析生成成功，长度: {len(explanation)} 字符")
        return explanation

    except Exception as e:
        error_msg = str(e)
        print(f"AI解析生成失败: {error_msg}")

        # 返回一个增强的基本解析
        return generate_fallback_explanation(question, student_answer)


def generate_fallback_explanation(question, student_answer):
    """当AI服务不可用时的备用解析生成"""
    explanation = f"""【错误定位】：你的答案"{student_answer}"与正确答案"{question.answer}"不符。

【错误原因】：可能是对题目理解有偏差或相关知识点掌握不够牢固。

【正确解法】：正确答案是"{question.answer}"。

【修正建议】：
1. 重新仔细阅读题目，理解题意
2. 复习相关知识点
3. 多做类似题目加强练习

【知识点提醒】：这道题考查的是{getattr(question, 'subject', '相关学科')}的基础知识，需要扎实掌握。"""

    return explanation


def analyze_answer_pattern(student_answer, correct_answer, question_type):
    """分析答案模式，提供更精确的错误类型判断"""
    analysis = {
        'error_type': 'unknown',
        'similarity_score': 0,
        'suggestions': []
    }

    if not student_answer or not student_answer.strip():
        analysis['error_type'] = 'no_answer'
        analysis['suggestions'].append('请确保完整作答所有题目')
        return analysis

    student_clean = student_answer.strip().lower()
    correct_clean = correct_answer.strip().lower()

    # 计算相似度
    from difflib import SequenceMatcher
    analysis['similarity_score'] = SequenceMatcher(None, student_clean, correct_clean).ratio()

    if question_type in ['MC', '选择题']:
        # 选择题特殊处理
        if len(student_clean) == 1 and len(correct_clean) == 1:
            if student_clean != correct_clean:
                analysis['error_type'] = 'wrong_option'
                analysis['suggestions'].append('请仔细阅读各个选项，注意区分相似选项')
        else:
            analysis['error_type'] = 'format_error'
            analysis['suggestions'].append('选择题请只填写选项字母（如A、B、C、D）')

    elif question_type in ['TF', '判断题']:
        # 判断题处理
        if '正确' in student_clean or '对' in student_clean or 'true' in student_clean:
            student_bool = True
        elif '错误' in student_clean or '错' in student_clean or 'false' in student_clean:
            student_bool = False
        else:
            analysis['error_type'] = 'format_error'
            analysis['suggestions'].append('判断题请回答"正确"或"错误"')
            return analysis

        correct_bool = '正确' in correct_clean or '对' in correct_clean or 'true' in correct_clean

        if student_bool != correct_bool:
            analysis['error_type'] = 'logic_error'
            analysis['suggestions'].append('请重新分析题目的逻辑关系')

    else:
        # 主观题处理
        if analysis['similarity_score'] > 0.8:
            analysis['error_type'] = 'minor_error'
            analysis['suggestions'].append('答案基本正确，注意细节表达')
        elif analysis['similarity_score'] > 0.5:
            analysis['error_type'] = 'partial_correct'
            analysis['suggestions'].append('答案部分正确，需要补充完善')
        else:
            analysis['error_type'] = 'major_error'
            analysis['suggestions'].append('答案偏差较大，建议重新学习相关知识点')

    return analysis


@login_required
def online_learning_assistant(request):
    if request.user.role != "student":
        return redirect("home")

    ai_response = None
    if request.method == "POST":
        student_question = request.POST.get("student_question", "")
        if student_question:
            try:
                # Provide context to the AI based on the student's recent wrong answers
                wrong_questions_context = StudentResponse.objects.filter(
                    student=request.user,
                    is_correct=False
                ).select_related('question')[:5] # Limit to recent 5 for context
                
                context_prompt_part = ""
                if wrong_questions_context.exists():
                    context_prompt_part = "请结合该学生最近的以下错题背景，以便更有针对性地回答：\n"
                    for resp in wrong_questions_context:
                        context_prompt_part += f"- {resp.question.question_text}\n"

                client = OpenAI(
                    api_key=settings.DASHSCOPE_API_KEY,
                    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                )

                completion = client.chat.completions.create(
                    model="qwen-turbo",
                    messages=[
                        {"role": "system", "content": f"你是一个教学严谨、乐于助人的AI学习助手。{context_prompt_part}"},
                        {"role": "user", "content": student_question},
                    ],
                )
                ai_response = completion.choices[0].message.content
            except Exception as e:
                ai_response = f"抱歉，AI助手暂时无法回答，请稍后再试。错误: {e}"

    # Fetch all incorrect responses for the "Wrong Questions Notebook"
    wrong_questions = StudentResponse.objects.filter(
        student=request.user,
        is_correct=False
    ).select_related('question', 'assessment').order_by('-submitted_at')

    context = {
        'wrong_questions': wrong_questions,
        'ai_response': ai_response,
    }
    
    return render(request, "student_dashboard/online_learning_assistant.html", context)


@login_required
def realtime_practice_evaluation(request):
    if request.user.role != "student":
        return redirect("home")

    # 统一获取科目列表
    subjects = Assessment.objects.values_list('title', flat=True).distinct()
    
    # 初始化上下文，并预置用户选择的默认值
    context = {
        'subjects': subjects,
        'selected_subject': '',
        'selected_difficulty': '入门',
        'num_questions': 1
    }

    client = OpenAI(
        api_key=settings.DASHSCOPE_API_KEY,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

    if request.method == "POST":
        action = request.POST.get("action")

        # 保存用户选择，以便在页面刷新后恢复
        context.update({
            'selected_subject': request.POST.get("subject"),
            'selected_difficulty': request.POST.get("difficulty"),
            'num_questions': request.POST.get("num_questions", 1)
        })

        if action == "generate_question":
            subject = context['selected_subject']
            difficulty = context['selected_difficulty']
            num_questions = context['num_questions']

            # 1. 获取该学生的历史错题
            wrong_questions = StudentResponse.objects.filter(student=request.user, is_correct=False)
            wrong_questions_text = "\n".join([f"- {wq.question.question_text}" for wq in wrong_questions])
            
            history_prompt_part = ""
            if wrong_questions.exists():
                history_prompt_part = f"该学生过去在以下题目上犯过错误，请重点针对这些知识盲区出题：\n{wrong_questions_text}\n"

            try:
                # 2. AI根据历史错题和新要求生成练习题
                # 新的通用指令，让AI混合生成题目
                system_prompt = (
                    "你是一位私人辅导老师，请生成一组合适的练习题。你可以混合生成选择题、判断题和简答题。"
                    "请严格按照'题目：[内容]\\n答案：[答案]\\n###'的格式为每一道题输出，不要有任何额外说明。"
                    "格式细节："
                    "1. 选择题：'题目：'部分包含题干和以'A. '开头的选项，'答案：'部分只有选项字母。"
                    "2. 判断题：'题目：'部分是一个陈述句，'答案：'部分是'正确'或'错误'。"
                    "3. 简答题：'题目：'部分是问题，'答案：'部分是参考答案。"
                )

                prompt_messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"{history_prompt_part}现在，请为我生成{num_questions}道关于“{subject}”的“{difficulty}”难度练习题。"}
                ]
                completion = client.chat.completions.create(model="qwen-turbo", messages=prompt_messages)
                raw_content = completion.choices[0].message.content

                # 3. 解析题目和答案
                questions_data = []
                question_blocks = raw_content.strip().split('###')
                for block in question_blocks:
                    if not block.strip():
                        continue
                    try:
                        q_text_match = re.search(r"题目：(.*?)\n答案：", block, re.DOTALL)
                        ans_text_match = re.search(r"答案：(.*)", block, re.DOTALL)

                        if q_text_match and ans_text_match:
                            q_text = q_text_match.group(1).strip()
                            ans_text = ans_text_match.group(1).strip()
                            
                            # 动态推断题目类型
                            question_type = '简答题' # 默认
                            if ans_text.strip() in ['正确', '错误']:
                                question_type = '判断题'
                            elif re.search(r'\n[A-Z]\.', q_text):
                                question_type = '选择题'

                            parsed_question = {
                                "question_type": question_type,
                                "correct_answer": ans_text
                            }

                            if question_type == '选择题':
                                # 分离题干和选项
                                parts = q_text.split('\n')
                                stem = parts[0]
                                options = [part.strip() for part in parts[1:] if part.strip()]
                                parsed_question["question_text"] = stem  # 使用question_text字段
                                parsed_question["options"] = options
                            else:
                                parsed_question["question_text"] = q_text

                            questions_data.append(parsed_question)
                    except AttributeError:
                        continue

                request.session['practice_questions'] = questions_data
                context['practice_questions'] = questions_data

            except Exception as e:
                context['error'] = f"生成题目失败: {e}"

        elif action == "submit_answer":
            practice_questions = request.session.get('practice_questions', [])
            
            evaluation_results = []
            for i, question_data in enumerate(practice_questions, 1):
                student_answer = request.POST.get(f"student_answer_{i}", "").strip()
                correct_answer = question_data['correct_answer']
                
                # 对选择题答案做特殊处理，只取选项字母
                if question_data.get('question_type') == '选择题' and '.' in student_answer:
                    student_answer_processed = student_answer.split('.')[0].strip()
                else:
                    student_answer_processed = student_answer

                is_correct = student_answer_processed.lower() == correct_answer.lower()
                
                question_text_for_prompt = ""
                if question_data.get('question_type') == '选择题':
                    question_text_for_prompt = f"{question_data.get('question_text', '')}\n" + "\n".join(question_data.get('options', []))
                else:
                    question_text_for_prompt = question_data.get('question_text', '')

                try:
                    # 为所有题目（无论对错）生成AI建议
                    if is_correct:
                        system_prompt_suggestion = "你是一位优秀的辅导老师。学生答对了这道题。请对学生的回答表示肯定，并可以简要地补充一些相关的知识点或有趣的背景作为鼓励。让内容简短、精炼、有趣。"
                        user_prompt_suggestion = f"这道题我答对了！\n题目是：'{question_text_for_prompt}'\n我的答案是：'{student_answer}'\n请给我一些鼓励和拓展知识。"
                    else:
                        system_prompt_suggestion = "你是一位优秀的辅导老师。请简明扼要地分析学生错误的原因，点出核心知识点，并给出学习建议。保持回答简短精炼。"
                        user_prompt_suggestion = f"问题是：'{question_text_for_prompt}'\n正确答案是：'{correct_answer}'\n我的错误答案是：'{student_answer}'\n\n请帮我分析一下为什么错了，并给出学习建议。"

                    prompt_messages = [
                        {"role": "system", "content": system_prompt_suggestion},
                        {"role": "user", "content": user_prompt_suggestion}
                    ]
                    completion = client.chat.completions.create(model="qwen-turbo", messages=prompt_messages)
                    suggestion = completion.choices[0].message.content
                except Exception as e:
                    suggestion = f"生成解析失败: {e}"
                
                evaluation_results.append({
                    "student_answer": student_answer,
                    "is_correct": is_correct,
                    "suggestion": suggestion
                })
            
            context['evaluation_results'] = evaluation_results
            context['practice_questions'] = practice_questions # 将题目传回以便显示
            # 清除session中的题目，避免重复提交
            if 'practice_questions' in request.session:
                del request.session['practice_questions']


    return render(request, "student_dashboard/realtime_practice_evaluation.html", context)

@login_required
def master_question(request, response_id):
    if request.method == 'POST':
        try:
            # Ensure the response belongs to the logged-in student to prevent unauthorized access
            response = get_object_or_404(StudentResponse, id=response_id, student=request.user)
            
            # Mark the question as mastered (if field exists)
            try:
                if hasattr(response, 'is_mastered'):
                    response.is_mastered = True
                    response.save(update_fields=['is_mastered'])
            except Exception:
                # Graceful fallback if field doesn't exist
                pass
            
            return JsonResponse({'status': 'success', 'message': '错题已标记为“已掌握”'})
        except StudentResponse.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '未找到该答题记录'}, status=404)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
    
    return JsonResponse({'status': 'error', 'message': '无效的请求方法'}, status=405)

@login_required
def courseware_library_view(request):
    """
    Displays all available courseware to students, grouped by subject.
    """
    if not request.user.is_student:
        return redirect('users:login')

    all_coursewares = Courseware.objects.order_by('subject', '-upload_date')
    
    courseware_by_subject = {}
    for courseware in all_coursewares:
        if courseware.subject not in courseware_by_subject:
            courseware_by_subject[courseware.subject] = []
        courseware_by_subject[courseware.subject].append(courseware)
        
    context = {
        'courseware_by_subject': courseware_by_subject,
        'page_title': '学习资源库'
    }
    
    return render(request, 'student_dashboard/courseware_library.html', context)

@login_required
def test_page(request):
    """测试页面，用于验证学生端功能"""
    if request.user.role != "student":
        return redirect("home")

    return render(request, 'student_dashboard/test_page.html')

@login_required
def demo_page(request):
    """演示页面，展示学生端所有功能"""
    if request.user.role != "student":
        return redirect("home")

    return render(request, 'student_dashboard/demo_page.html')


@login_required
def test_ai_grading_view(request):
    """测试AI批改功能的视图"""
    if request.user.role != "student":
        return redirect("home")

    result = None

    if request.method == "POST":
        # 创建一个测试题目
        test_question_text = request.POST.get('question_text', '什么是Python？')
        correct_answer = request.POST.get('correct_answer', 'Python是一种编程语言')
        student_answer = request.POST.get('student_answer', '不知道')

        # 创建临时题目对象
        class TempQuestion:
            def __init__(self, text, answer):
                self.question_text = text
                self.answer = answer
                self.options = None
                self.question_type = 'SA'

        temp_question = TempQuestion(test_question_text, correct_answer)

        # 测试AI解析生成
        try:
            explanation = generate_ai_explanation(temp_question, student_answer)
            result = {
                'success': True,
                'question': test_question_text,
                'correct_answer': correct_answer,
                'student_answer': student_answer,
                'explanation': explanation
            }
        except Exception as e:
            result = {
                'success': False,
                'error': str(e)
            }

    return render(request, 'student_dashboard/test_ai_grading.html', {'result': result})

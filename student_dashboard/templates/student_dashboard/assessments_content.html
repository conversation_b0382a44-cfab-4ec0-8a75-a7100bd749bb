<!DOCTYPE html>
<html>
<head>
    <title>我的考核</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 0;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .assessments-container {
            padding: 30px;
        }

        .assessment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            position: relative;
        }

        .assessment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .assessment-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4e73df, #224abe);
        }

        .assessment-content {
            padding: 25px;
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .assessment-content:hover {
            color: inherit;
            text-decoration: none;
        }

        .assessment-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .assessment-meta {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .assessment-status {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-completed {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .status-pending {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 60px 30px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h5 {
            margin-bottom: 10px;
            color: #495057;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4e73df;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-tasks me-3"></i>我的考核
            </div>
            <div class="header-subtitle">
                查看考核任务，参与在线测试，追踪学习进度
            </div>
        </div>

        <!-- 考核列表 -->
        <div class="assessments-container">
            {% if assessments %}
                {% for assessment in assessments %}
                    <div class="assessment-card">
                        <a href="{% if assessment.id not in completed_assessment_ids %}{% url 'student_dashboard:assessment_detail' assessment.id %}{% else %}{% url 'student_dashboard:assessment_result' assessment.id %}{% endif %}"
                           target="_parent" class="assessment-content">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="assessment-title">{{ assessment.title }}</div>
                                    <div class="assessment-meta">
                                        <i class="fas fa-calendar-alt me-2"></i>发布于: {{ assessment.created_at|date:"Y年m月d日 H:i" }}
                                        <span class="ms-3">
                                            <i class="fas fa-user-tie me-2"></i>{{ assessment.teacher.username }} 老师
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    {% if assessment.id in completed_assessment_ids %}
                                        <span class="assessment-status status-completed">
                                            <i class="fas fa-check-circle me-2"></i>已完成
                                        </span>
                                    {% else %}
                                        <span class="assessment-status status-pending">
                                            <i class="fas fa-clock me-2"></i>待完成
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}

                <!-- 统计信息 -->
                <div class="stats-row">
                    <div class="stat-item">
                        <div class="stat-number">{{ assessments|length }}</div>
                        <div class="stat-label">总考核数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ completed_assessment_ids|length }}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-item">
                        {% with pending_count=assessments|length|add:completed_assessment_ids|length|add:"-1"|add:"-1" %}
                        <div class="stat-number">{{ assessments|length|add:completed_assessment_ids|length|add:"-1"|add:"-1" }}</div>
                        {% endwith %}
                        <div class="stat-label">待完成</div>
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h5>暂无考核任务</h5>
                    <p>老师还没有发布任何考核任务，请耐心等待。</p>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
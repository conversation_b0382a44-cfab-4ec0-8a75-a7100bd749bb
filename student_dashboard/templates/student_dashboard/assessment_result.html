<!DOCTYPE html>
<html>
<head>
    <title>考核结果 - {{ assessment.title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1000px;
        }

        .result-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            color: white;
            text-align: center;
        }

        .result-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .score-section {
            padding: 2rem;
            background: white;
        }

        .score-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 2rem;
            color: white;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .score-display {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .score-label {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .result-card {
            margin-bottom: 2rem;
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
        }

        .result-card.correct {
            border-left: 5px solid #28a745;
        }

        .result-card.incorrect {
            border-left: 5px solid #dc3545;
        }

        .question-header {
            padding: 1.5rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .question-header.correct {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }

        .question-header.incorrect {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }

        .question-body {
            padding: 2rem;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .answer-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .student-answer {
            color: #007bff;
            font-weight: 600;
        }

        .correct-answer {
            color: #28a745;
            font-weight: 600;
        }

        .explanation-section {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
            border-left: 4px solid #ffc107;
        }

        .explanation-title {
            color: #856404;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .explanation-text {
            color: #856404;
            line-height: 1.6;
        }

        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
        }

        .status-badge.correct {
            background: #28a745;
            color: white;
        }

        .status-badge.incorrect {
            background: #dc3545;
            color: white;
        }

        .back-section {
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 0 0 20px 20px;
            text-align: center;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .collapse-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            color: white;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .collapse-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <!-- 结果标题区域 -->
            <div class="result-header">
                <div class="result-title">
                    <i class="fas fa-chart-line me-3"></i>考核结果
                </div>
                <div style="font-size: 1.2rem; opacity: 0.9;">{{ assessment.title }}</div>
            </div>

            <!-- 分数展示区域 -->
            <div class="score-section">
                <div class="score-card">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="score-display">{{ correct_count }} / {{ total_questions }}</div>
                            <div class="score-label">答对题数</div>
                        </div>
                        <div class="col-md-6">
                            <div class="score-display">{% widthratio correct_count total_questions 100 %}%</div>
                            <div class="score-label">正确率</div>
                        </div>
                    </div>
                </div>

                <!-- 题目详情 -->
                {% for result in results %}
                    <div class="result-card {% if result.is_correct %}correct{% else %}incorrect{% endif %}">
                        <div class="question-header {% if result.is_correct %}correct{% else %}incorrect{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>
                                        <i class="fas fa-question-circle me-2"></i>
                                        问题 {{ forloop.counter }}: 【{{ result.question.question_type }}】
                                    </strong>
                                </div>
                                <div>
                                    {% if result.is_correct %}
                                        <span class="status-badge correct">
                                            <i class="fas fa-check-circle me-1"></i> 正确
                                        </span>
                                    {% else %}
                                        <span class="status-badge incorrect">
                                            <i class="fas fa-times-circle me-1"></i> 错误
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="question-body">
                            <div class="question-text">{{ result.question.question_text|linebreaksbr }}</div>

                            <!-- 显示选项（如果有） -->
                            {% if result.question.options %}
                                <div class="mb-3">
                                    <strong>选项：</strong>
                                    <ul class="list-unstyled mt-2">
                                        {% for option in result.question.options %}
                                            <li class="mb-1">{{ option }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}

                            <div class="answer-section">
                                <div class="mb-2">
                                    <strong><i class="fas fa-user me-2"></i>你的答案:</strong>
                                    <span class="student-answer">{{ result.student_answer }}</span>
                                </div>

                                {% if not result.is_correct %}
                                    <div class="mb-2">
                                        <strong><i class="fas fa-check me-2"></i>正确答案:</strong>
                                        <span class="correct-answer">{{ result.question.answer }}</span>
                                    </div>
                                {% endif %}
                            </div>

                            {% if not result.is_correct and result.explanation %}
                                <div class="explanation-section">
                                    <div class="explanation-title">
                                        <i class="fas fa-lightbulb me-2"></i>AI智能解析
                                    </div>
                                    <div class="explanation-text">{{ result.explanation|linebreaksbr }}</div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- 返回按钮区域 -->
            <div class="back-section">
                <a href="{% url 'student_dashboard:dashboard' %}" class="back-btn">
                    <i class="fas fa-arrow-left me-2"></i>返回我的考核列表
                </a>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
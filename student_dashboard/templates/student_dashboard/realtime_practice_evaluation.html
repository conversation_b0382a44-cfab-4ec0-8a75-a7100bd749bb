<!DOCTYPE html>
<html>
<head>
    <title>实时练习与评估</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1200px;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .content-section {
            padding: 40px;
        }

        .practice-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
        }

        .step-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 20px 30px;
            border-bottom: none;
            display: flex;
            align-items: center;
        }

        .step-number {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 15px;
        }

        .step-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .card-body-custom {
            padding: 30px;
        }

        .intro-text {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .topic-input {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .topic-input:focus {
            border-color: #6f42c1;
            box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
        }

        .generate-btn {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(111, 66, 193, 0.4);
        }

        .question-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 4px solid #6f42c1;
        }

        .question-number {
            font-weight: 700;
            color: #6f42c1;
            margin-bottom: 10px;
        }

        .question-text {
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .answer-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        .answer-input:focus {
            border-color: #6f42c1;
            box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
        }

        .submit-answers-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .submit-answers-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .result-card {
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .result-correct {
            border-left: 5px solid #28a745;
        }

        .result-incorrect {
            border-left: 5px solid #dc3545;
        }

        .result-header {
            padding: 15px 20px;
            font-weight: 600;
        }

        .result-correct .result-header {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
        }

        .result-incorrect .result-header {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
        }

        .result-body {
            padding: 20px;
        }

        .ai-suggestion {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            border-left: 4px solid #ffc107;
        }

        .suggestion-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-weight: 600;
            color: #856404;
        }

        .suggestion-content {
            color: #495057;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-dumbbell me-3"></i>实时练习评估
            </div>
            <div class="header-subtitle">
                AI个性化练习 · 智能评估 · 实时反馈
            </div>
        </div>

        <div class="content-section">
            <div class="practice-card">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">选择练习主题</div>
                </div>
                <div class="card-body-custom">
                    <p class="intro-text">在这里，AI可以根据您的历史错题和学习情况，为您量身定制练习题，助您攻克薄弱环节。</p>

                    {% if error %}
                        <div class="alert alert-danger">{{ error }}</div>
                    {% endif %}

                    <form method="post" action="{% url 'student_dashboard:realtime_practice_evaluation' %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="generate_question">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="subject" class="form-label fw-bold">科目</label>
                                <select class="form-select topic-input" id="subject" name="subject" required>
                                    <option value="">请选择科目</option>
                                    {% for subject in subjects %}
                                        <option value="{{ subject }}" {% if subject == selected_subject %}selected{% endif %}>{{ subject }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="difficulty" class="form-label fw-bold">难度</label>
                                <select class="form-select topic-input" id="difficulty" name="difficulty">
                                    <option value="入门" {% if selected_difficulty == '入门' %}selected{% endif %}>入门</option>
                                    <option value="基础" {% if selected_difficulty == '基础' %}selected{% endif %}>基础</option>
                                    <option value="进阶" {% if selected_difficulty == '进阶' %}selected{% endif %}>进阶</option>
                                    <option value="挑战" {% if selected_difficulty == '挑战' %}selected{% endif %}>挑战</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="num_questions" class="form-label fw-bold">题目数量</label>
                                <input type="number" class="form-control topic-input" id="num_questions" name="num_questions"
                                       value="{{ num_questions|default:1 }}" min="1" max="10">
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary generate-btn">
                                <i class="fas fa-cogs me-2"></i>生成练习题
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 第二步：请作答 -->
            {% if practice_questions and not evaluation_results %}
            <div class="practice-card">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">请作答</div>
                </div>
                <div class="card-body-custom">
                    <form method="post" action="{% url 'student_dashboard:realtime_practice_evaluation' %}" id="answer-form">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="submit_answer">

                        {% for question in practice_questions %}
                        <div class="question-item">
                            <div class="question-number">题目 {{ forloop.counter }}:</div>
                            <div class="question-text">{{ question.question_text }}</div>

                            {% if question.question_type == '选择题' %}
                                <div class="ms-3">
                                    {% for option in question.options %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="student_answer_{{ forloop.parentloop.counter }}"
                                               id="option_{{ forloop.parentloop.counter }}_{{ forloop.counter }}" value="{{ option }}" required>
                                        <label class="form-check-label" for="option_{{ forloop.parentloop.counter }}_{{ forloop.counter }}">
                                            {{ option }}
                                        </label>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% elif question.question_type == '判断题' %}
                                <div class="ms-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="student_answer_{{ forloop.counter }}"
                                               id="option_{{ forloop.counter }}_true" value="正确" required>
                                        <label class="form-check-label" for="option_{{ forloop.counter }}_true">
                                            正确
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="student_answer_{{ forloop.counter }}"
                                               id="option_{{ forloop.counter }}_false" value="错误" required>
                                        <label class="form-check-label" for="option_{{ forloop.counter }}_false">
                                            错误
                                        </label>
                                    </div>
                                </div>
                            {% else %}
                                <div class="form-floating">
                                    <textarea class="form-control answer-input" name="student_answer_{{ forloop.counter }}"
                                              id="student_answer_{{ forloop.counter }}" style="height: 100px" required></textarea>
                                    <label for="student_answer_{{ forloop.counter }}">您的答案</label>
                                </div>
                            {% endif %}
                        </div>
                        {% endfor %}

                        <div class="text-center">
                            <button id="submit-btn" type="submit" class="btn btn-success submit-answers-btn">
                                <i class="fas fa-check-circle me-2"></i>提交答案
                            </button>
                        </div>
                        <div id="loading-message" class="alert alert-info mt-3" style="display: none;">
                            <i class="fas fa-spinner fa-spin me-2"></i> 答案已提交，AI 正在批改中，请稍候...
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}

            <!-- 第三步：查看结果 -->
            {% if evaluation_results %}
            <div class="practice-card">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">练习结果与建议</div>
                </div>
                <div class="card-body-custom">

                    {% for result in evaluation_results %}
                    <div class="result-card {% if result.is_correct %}result-correct{% else %}result-incorrect{% endif %}">
                        <div class="result-header">
                            题目 {{ forloop.counter }} - {% if result.is_correct %}回答正确 ✓{% else %}回答错误 ✗{% endif %}
                        </div>
                        <div class="result-body">
                            <div class="mb-3">
                                <strong>题目：</strong>
                                {% with practice_questions|slice:":forloop.counter"|last as question %}
                                    {% if question.question_type == '选择题' %}
                                        <div class="question-text">{{ question.question_text }}</div>
                                        {% if question.options %}
                                        <ul class="list-unstyled ps-3 mt-2">
                                        {% for option in question.options %}
                                            <li>{{ option }}</li>
                                        {% endfor %}
                                        </ul>
                                        {% endif %}
                                    {% else %}
                                        <div class="question-text">{{ question.question_text }}</div>
                                    {% endif %}
                                    <p class="mt-2"><strong>正确答案：</strong> <span class="correct-answer">{{ question.correct_answer }}</span></p>
                                {% endwith %}
                            </div>
                            <p><strong>你的答案：</strong> <span class="{% if result.is_correct %}correct-answer{% else %}student-answer{% endif %}">{{ result.student_answer }}</span></p>

                            <div class="ai-suggestion">
                                <div class="suggestion-header">
                                    <i class="fas fa-lightbulb me-2"></i>AI智能解析：
                                </div>
                                <div class="suggestion-content">
                                    {{ result.suggestion|linebreaksbr }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    <!-- 总结 -->
                    <div class="mt-5 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);">
                            <div class="card-body p-4">
                                <h4 class="card-title mb-3"><i class="fas fa-chart-bar me-2 text-primary"></i>练习总结</h4>
                                <div class="row text-center mb-3">
                                    <div class="col-md-4">
                                        <div class="fs-1 fw-bold text-primary">{{ evaluation_results|length }}</div>
                                        <div class="text-muted">总题数</div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="fs-1 fw-bold text-success">{{ correct_count }}</div>
                                        <div class="text-muted">答对题数</div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="fs-1 fw-bold {% if correct_rate >= 80 %}text-success{% elif correct_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ correct_rate }}%
                                        </div>
                                        <div class="text-muted">正确率</div>
                                    </div>
                                </div>
                                <div class="alert {% if correct_rate >= 80 %}alert-success{% elif correct_rate >= 60 %}alert-warning{% else %}alert-danger{% endif %} mb-0">
                                    {% if correct_rate >= 80 %}
                                        <i class="fas fa-trophy me-2"></i>太棒了！您对这个主题已经有很好的掌握。
                                    {% elif correct_rate >= 60 %}
                                        <i class="fas fa-thumbs-up me-2"></i>不错的表现！继续努力，您可以做得更好。
                                    {% else %}
                                        <i class="fas fa-book-open me-2"></i>这个主题还需要更多练习，不要气馁，继续加油！
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <a href="{% url 'student_dashboard:realtime_practice_evaluation' %}" class="btn btn-primary generate-btn">
                            <i class="fas fa-redo me-2"></i>再来一次
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const answerForm = document.getElementById('answer-form');
        if (answerForm) {
            answerForm.addEventListener('submit', function() {
                const submitBtn = document.getElementById('submit-btn');
                const loadingMsg = document.getElementById('loading-message');
                if (submitBtn && loadingMsg) {
                    submitBtn.style.display = 'none';
                    loadingMsg.style.display = 'block';
                }
            });
        }
    });
</script>
</body>
</html> 
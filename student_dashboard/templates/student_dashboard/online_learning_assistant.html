<!DOCTYPE html>
<html>
<head>
    <title>在线学习助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1200px;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .content-section {
            padding: 40px;
        }

        .ai-chat-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
        }

        .chat-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px 30px;
            border-bottom: none;
        }

        .chat-header h4 {
            margin: 0;
            font-weight: 600;
        }

        .chat-body {
            padding: 30px;
        }

        .question-input {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            resize: vertical;
            min-height: 120px;
        }

        .question-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .submit-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .ai-response {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: none;
            border-radius: 15px;
            padding: 25px;
            margin-top: 25px;
            position: relative;
        }

        .ai-response::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2196f3, #1976d2);
            border-radius: 15px 15px 0 0;
        }

        .response-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-weight: 600;
            color: #1565c0;
        }

        .response-content {
            color: #0d47a1;
            line-height: 1.6;
        }

        .wrong-questions-section {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .wrong-questions-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .wrong-questions-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .wrong-questions-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .wrong-questions-body {
            padding: 30px;
        }

        .question-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 25px;
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }

        .question-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .question-card-header {
            background: #f8f9fa;
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .question-meta {
            flex-grow: 1;
        }

        .question-id {
            font-weight: 600;
            color: #495057;
        }

        .assessment-name {
            color: #6c757d;
            font-size: 0.9rem;
            margin-left: 10px;
        }

        .master-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 20px;
            padding: 8px 20px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .master-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .question-card-body {
            padding: 25px;
        }

        .question-text {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .answer-section {
            margin: 15px 0;
        }

        .student-answer {
            color: #dc3545;
            font-weight: 600;
        }

        .correct-answer {
            color: #28a745;
            font-weight: 600;
        }

        .ai-explanation {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }

        .explanation-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-weight: 600;
            color: #856404;
        }

        .explanation-content {
            color: #495057;
            line-height: 1.6;
        }

        .empty-state {
            text-align: center;
            padding: 60px 30px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .wrong-question-card {
            transition: opacity 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-robot me-3"></i>AI 学习助手
            </div>
            <div class="header-subtitle">
                智能问答助手 · 错题本管理 · 个性化学习指导
            </div>
        </div>

        <div class="content-section">
            <!-- AI 问答助手 -->
            <div class="ai-chat-card">
                <div class="chat-header">
                    <h4><i class="fas fa-comments me-2"></i>智能问答助手</h4>
                </div>
                <div class="chat-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-4">
                            <label for="student_question" class="form-label fw-bold">向AI提问:</label>
                            <textarea class="form-control question-input" id="student_question" name="student_question"
                                    placeholder="在这里输入你遇到的学习问题，AI助手会为你详细解答...">{{ request.POST.student_question }}</textarea>
                        </div>
                        <button type="submit" id="ask-ai-btn" class="btn btn-primary submit-btn">
                            <span class="btn-text"><i class="fas fa-paper-plane me-2"></i>提交问题</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                            <span class="loading-text d-none">AI正在思考中...</span>
                        </button>
                    </form>

                    {% if ai_response %}
                        <div class="ai-response">
                            <div class="response-header">
                                <i class="fas fa-robot me-2"></i>AI助手回答:
                            </div>
                            <div class="response-content">
                                {{ ai_response|linebreaksbr }}
                            </div>
                        </div>
                    {% endif %}

                    {% if error %}
                        <div class="alert alert-danger mt-3">{{ error }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- 错题本 -->
            <div class="wrong-questions-section">
                <div class="wrong-questions-header">
                    <div class="wrong-questions-title">
                        <i class="fas fa-book-open me-3"></i>我的错题本
                    </div>
                    <div class="wrong-questions-subtitle">
                        温故而知新，可以为师矣
                    </div>
                </div>
                <div class="wrong-questions-body">
                    <div id="wrong-questions-container">
                        {% for response in wrong_questions %}
                            <div class="question-card wrong-question-card" id="response-card-{{ response.id }}">
                                <div class="question-card-header">
                                    <div class="question-meta">
                                        <span class="question-id">问题ID: {{ response.question.id }}</span>
                                        <span class="assessment-name">出自: {{ response.assessment.title }}</span>
                                    </div>
                                    <button class="btn master-btn" data-response-id="{{ response.id }}">
                                        <i class="fas fa-check-circle me-1"></i> 我已掌握
                                    </button>
                                </div>
                                <div class="question-card-body">
                                    <div class="question-text">
                                        <strong>题目：</strong>{{ response.question.question_text|linebreaksbr }}
                                    </div>
                                    {% if response.question.options %}
                                        <div class="mb-3">
                                            {% for option in response.question.options %}
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" disabled>
                                                    <label class="form-check-label">{{ option }}</label>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="answer-section">
                                        <p><strong>你的答案:</strong> <span class="student-answer">{{ response.answer }}</span></p>
                                        <p><strong>正确答案:</strong> <span class="correct-answer">{{ response.question.correct_answer }}</span></p>
                                    </div>
                                    {% if response.explanation %}
                                        <div class="ai-explanation">
                                            <div class="explanation-header">
                                                <i class="fas fa-lightbulb me-2"></i>AI智能解析:
                                            </div>
                                            <div class="explanation-content">
                                                {{ response.explanation|linebreaksbr }}
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% empty %}
                            <div class="empty-state">
                                <i class="fas fa-trophy"></i>
                                <h5>太棒了！</h5>
                                <p>你的错题本是空的，继续保持！</p>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // AI Assistant Form Submission
    const aiForm = document.querySelector('form[method="post"]');
    if (aiForm) {
        aiForm.addEventListener('submit', function() {
            const askBtn = document.getElementById('ask-ai-btn');
            const btnText = askBtn.querySelector('.btn-text');
            const spinner = askBtn.querySelector('.spinner-border');
            const loadingText = askBtn.querySelector('.loading-text');

            askBtn.disabled = true;
            btnText.classList.add('d-none');
            spinner.classList.remove('d-none');
            loadingText.classList.remove('d-none');
        });
    }

    // Wrong Questions Notebook "Master" button logic
    const csrftoken = document.querySelector('[name=csrf-token]').getAttribute('content');

    document.querySelectorAll('.master-btn').forEach(button => {
        button.addEventListener('click', function () {
            const responseId = this.dataset.responseId;
            const card = document.getElementById('response-card-' + responseId);

            fetch(`{% url 'student_dashboard:master_question' 0 %}`.replace('0', responseId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrftoken,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('网络请求失败。');
            })
            .then(data => {
                if (data.status === 'success') {
                    card.style.opacity = '0';
                    setTimeout(() => {
                        card.remove();
                        // Check if container is empty
                        if (document.getElementById('wrong-questions-container').childElementCount === 0) {
                            document.getElementById('wrong-questions-container').innerHTML = `
                                <div class="text-center py-5">
                                    <i class="fas fa-trophy fa-3x text-success"></i>
                                    <h4 class="mt-3">太棒了！</h4>
                                    <p class="text-muted">你的错题本是空的，继续保持！</p>
                                </div>`;
                        }
                    }, 500);
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作时发生错误。');
            });
        });
    });
});
</script>
</body>
</html> 
<!DOCTYPE html>
<html>
<head>
    <title>{{ assessment.title }} - 考核详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 900px;
        }

        .assessment-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            color: white;
            text-align: center;
        }

        .assessment-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .teacher-info {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .question-card {
            margin-bottom: 2rem;
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
        }

        .question-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.2rem 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .question-body {
            padding: 2rem 1.5rem;
            background: white;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .form-check {
            margin-bottom: 1rem;
            padding: 0.8rem 1rem;
            border-radius: 10px;
            transition: all 0.2s ease;
            border: 2px solid transparent;
        }

        .form-check:hover {
            background-color: #f8f9ff;
            border-color: #667eea;
        }

        .form-check-input:checked + .form-check-label {
            color: #667eea;
            font-weight: 600;
        }

        .form-check-input {
            width: 1.2rem;
            height: 1.2rem;
            margin-top: 0.2rem;
        }

        .form-check-label {
            font-size: 1rem;
            line-height: 1.5;
            margin-left: 0.5rem;
            cursor: pointer;
        }

        .answer-textarea {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            resize: vertical;
            min-height: 120px;
        }

        .answer-textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .submit-section {
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 0 0 20px 20px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 1rem 3rem;
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .back-btn {
            background: white;
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 50px;
            padding: 0.8rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }

        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
            z-index: 1050;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            flex-direction: column;
            backdrop-filter: blur(10px);
        }

        .loading-spinner {
            width: 4rem;
            height: 4rem;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.3rem;
            font-weight: 600;
            margin-top: 1rem;
            text-align: center;
        }

        .progress-indicator {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .progress-text {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }

        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">
            <div>正在智能批改中...</div>
            <div style="font-size: 1rem; margin-top: 0.5rem; opacity: 0.8;">AI正在分析您的答案，请稍候</div>
        </div>
    </div>

    <div class="container">
        <div class="main-container">
            <!-- 考核标题区域 -->
            <div class="assessment-header">
                <div class="assessment-title">{{ assessment.title }}</div>
                <div class="teacher-info">
                    <i class="fas fa-user-tie me-2"></i>由 {{ assessment.teacher.username }} 老师发布
                </div>
            </div>

            <!-- 进度指示器 -->
            <div class="progress-indicator">
                <div class="progress-text">
                    <i class="fas fa-tasks me-2"></i>答题进度: <span id="progress-count">0</span> / {{ assessment.questions.count }}
                </div>
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%" id="progress-bar"></div>
                </div>
            </div>

            <!-- 题目区域 -->
            <div style="padding: 0 2rem;">
                <form method="POST" action="{% url 'student_dashboard:assessment_detail' assessment.id %}" id="assessment-form">
                    {% csrf_token %}
                    {% for question in assessment.questions.all %}
                        <div class="question-card" data-question-index="{{ forloop.counter }}">
                            <div class="question-header">
                                <i class="fas fa-question-circle me-2"></i>
                                问题 {{ forloop.counter }}: 【{{ question.question_type }}】
                            </div>
                            <div class="question-body">
                                <div class="question-text">{{ question.question_text|linebreaksbr }}</div>

                                {% if question.options %}
                                    <div class="options-container">
                                        {% for option in question.options %}
                                            <div class="form-check">
                                                <input class="form-check-input question-input" type="radio"
                                                       name="question_{{ question.id }}"
                                                       id="option_{{ question.id }}_{{ forloop.counter }}"
                                                       value="{{ option }}"
                                                       data-question-id="{{ question.id }}">
                                                <label class="form-check-label" for="option_{{ question.id }}_{{ forloop.counter }}">
                                                    {{ option }}
                                                </label>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="answer-input-container">
                                        <label for="answer_{{ question.id }}" class="form-label">
                                            <i class="fas fa-edit me-2"></i>请输入您的答案:
                                        </label>
                                        <textarea class="form-control answer-textarea question-input"
                                                  id="answer_{{ question.id }}"
                                                  name="question_{{ question.id }}"
                                                  placeholder="请在此处输入您的详细答案..."
                                                  data-question-id="{{ question.id }}"></textarea>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </form>
            </div>

            <!-- 提交区域 -->
            <div class="submit-section">
                <div class="d-grid gap-3">
                    <button type="submit" form="assessment-form" id="submit-assessment-btn" class="submit-btn">
                        <i class="fas fa-brain me-2"></i> 提交并获取AI智能批改
                    </button>
                    <div class="text-center">
                        <a href="{% url 'student_dashboard:assessments_content' %}" class="back-btn">
                            <i class="fas fa-arrow-left me-2"></i>返回考核列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const totalQuestions = {{ assessment.questions.count }};
            const progressBar = document.getElementById('progress-bar');
            const progressCount = document.getElementById('progress-count');
            const submitBtn = document.getElementById('submit-assessment-btn');
            const questionInputs = document.querySelectorAll('.question-input');

            // 跟踪已回答的问题
            function updateProgress() {
                const answeredQuestions = new Set();

                questionInputs.forEach(input => {
                    const questionId = input.dataset.questionId;
                    if (input.type === 'radio' && input.checked) {
                        answeredQuestions.add(questionId);
                    } else if (input.type === 'textarea' && input.value.trim()) {
                        answeredQuestions.add(questionId);
                    }
                });

                const answeredCount = answeredQuestions.size;
                const progressPercent = (answeredCount / totalQuestions) * 100;

                progressBar.style.width = progressPercent + '%';
                progressCount.textContent = answeredCount;

                // 更新提交按钮状态
                if (answeredCount === totalQuestions) {
                    submitBtn.classList.remove('btn-secondary');
                    submitBtn.classList.add('submit-btn');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-brain me-2"></i> 提交并获取AI智能批改';
                } else {
                    submitBtn.classList.add('btn-secondary');
                    submitBtn.classList.remove('submit-btn');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i> 请完成所有题目 (${answeredCount}/${totalQuestions})`;
                }
            }

            // 为所有输入添加事件监听器
            questionInputs.forEach(input => {
                if (input.type === 'radio') {
                    input.addEventListener('change', updateProgress);
                } else {
                    input.addEventListener('input', updateProgress);
                }
            });

            // 初始化进度
            updateProgress();

            // 表单提交处理
            document.getElementById('assessment-form').addEventListener('submit', function(e) {
                const answeredQuestions = new Set();

                questionInputs.forEach(input => {
                    const questionId = input.dataset.questionId;
                    if (input.type === 'radio' && input.checked) {
                        answeredQuestions.add(questionId);
                    } else if (input.type === 'textarea' && input.value.trim()) {
                        answeredQuestions.add(questionId);
                    }
                });

                if (answeredQuestions.size < totalQuestions) {
                    e.preventDefault();
                    alert('请完成所有题目后再提交！');
                    return false;
                }

                // 显示加载动画
                document.getElementById('loading-overlay').style.display = 'flex';

                // 禁用提交按钮防止重复提交
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> 提交中...';
            });

            // 添加题目卡片动画效果
            const questionCards = document.querySelectorAll('.question-card');
            questionCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 平滑滚动到下一题
            questionInputs.forEach((input, index) => {
                input.addEventListener('change', function() {
                    if (this.type === 'radio') {
                        setTimeout(() => {
                            const nextCard = this.closest('.question-card').nextElementSibling;
                            if (nextCard && nextCard.classList.contains('question-card')) {
                                nextCard.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center'
                                });
                            }
                        }, 300);
                    }
                });
            });
        });
    </script>
</body>
</html> 
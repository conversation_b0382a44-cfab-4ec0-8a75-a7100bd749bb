<!DOCTYPE html>
<html>
<head>
    <title>测试AI批改功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 800px;
            padding: 2rem;
        }
        
        .test-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 15px;
            padding: 2rem;
            color: white;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .result-section {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-radius: 15px;
            padding: 2rem;
            border-left: 5px solid #28a745;
        }
        
        .error-section {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-radius: 15px;
            padding: 2rem;
            border-left: 5px solid #dc3545;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 1rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <div class="test-header">
                <h1><i class="fas fa-robot me-3"></i>AI批改功能测试</h1>
                <p class="mb-0">测试AI智能批改和解析生成功能</p>
            </div>
            
            <div class="form-section">
                <h3><i class="fas fa-edit me-2"></i>输入测试数据</h3>
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="question_text" class="form-label">题目内容</label>
                        <textarea class="form-control" id="question_text" name="question_text" rows="3" placeholder="请输入题目内容...">{{ request.POST.question_text|default:"什么是Python编程语言？" }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="correct_answer" class="form-label">正确答案</label>
                        <input type="text" class="form-control" id="correct_answer" name="correct_answer" placeholder="请输入正确答案..." value="{{ request.POST.correct_answer|default:'Python是一种高级编程语言' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="student_answer" class="form-label">学生答案（模拟错误答案）</label>
                        <input type="text" class="form-control" id="student_answer" name="student_answer" placeholder="请输入学生的错误答案..." value="{{ request.POST.student_answer|default:'不知道' }}">
                    </div>
                    
                    <button type="submit" class="btn btn-test">
                        <i class="fas fa-brain me-2"></i>测试AI批改
                    </button>
                </form>
            </div>
            
            {% if result %}
                {% if result.success %}
                    <div class="result-section">
                        <h3><i class="fas fa-check-circle me-2"></i>AI批改结果</h3>
                        
                        <div class="mb-3">
                            <strong>题目：</strong>{{ result.question }}
                        </div>
                        
                        <div class="mb-3">
                            <strong>正确答案：</strong><span class="text-success">{{ result.correct_answer }}</span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>学生答案：</strong><span class="text-danger">{{ result.student_answer }}</span>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-lightbulb me-2"></i>AI智能解析：</h5>
                            <div class="p-3 bg-white rounded border">
                                {{ result.explanation|linebreaksbr }}
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="error-section">
                        <h3><i class="fas fa-exclamation-triangle me-2"></i>测试失败</h3>
                        <p><strong>错误信息：</strong>{{ result.error }}</p>
                    </div>
                {% endif %}
            {% endif %}
            
            <div class="text-center mt-4">
                <a href="{% url 'student_dashboard:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回学生仪表板
                </a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

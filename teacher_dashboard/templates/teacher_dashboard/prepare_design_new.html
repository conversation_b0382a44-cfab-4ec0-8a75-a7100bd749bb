<!DOCTYPE html>
<html>
<head>
    <title>AI赋能备课</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .content-section {
            padding: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .feature-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
        }

        .feature-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .feature-card-body {
            padding: 30px;
        }

        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
        }

        .upload-area.dragover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }

        .upload-icon {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.1rem;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .action-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
            color: white;
        }

        .action-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .secondary-btn:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .success-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .success-btn:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .ai-result-area {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 15px;
            padding: 30px;
            min-height: 300px;
            border: 1px solid #e2e8f0;
        }

        .result-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 60px 20px;
        }

        .result-placeholder i {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .teaching-plan-item {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .teaching-plan-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .plan-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .plan-meta {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .plan-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-sm-custom {
            padding: 8px 16px;
            font-size: 0.85rem;
            border-radius: 20px;
            font-weight: 500;
        }

        .question-type-selector {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .question-type-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: white;
            border-radius: 10px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
        }

        .question-type-label {
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .question-type-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: 600;
        }

        .type-mc { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
        .type-tf { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .type-sa { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .type-pg { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }

        .count-input {
            width: 80px;
            text-align: center;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 20px;
        }

        .spinner-custom {
            width: 20px;
            height: 20px;
        }

        .ai-result-area {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 15px;
            padding: 30px;
            min-height: 300px;
            border: 1px solid #e2e8f0;
            margin-top: 20px;
        }

        .result-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 60px 20px;
        }

        .result-placeholder i {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .teaching-plan-item {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .teaching-plan-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .plan-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .plan-meta {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .plan-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-sm-custom {
            padding: 8px 16px;
            font-size: 0.85rem;
            border-radius: 20px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-brain me-3"></i>AI赋能备课
            </div>
            <div class="header-subtitle">
                智能教案生成 · 个性化备课 · 高效教学设计
            </div>
        </div>

        <div class="content-section">
            <!-- 文件上传区域 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-upload me-3 text-primary"></i>上传教学资料
                    </h5>
                </div>
                <div class="feature-card-body">
                    <form id="teaching-document-form" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="upload-area" onclick="document.getElementById('teaching-document-input').click()">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">点击上传或拖拽文件到此处</div>
                            <div class="upload-hint">支持 .pdf, .docx, .txt 等格式，最大50MB</div>
                            <input type="file" class="file-input" id="teaching-document-input" name="teaching_document" multiple accept=".pdf,.docx,.txt,.doc">
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="action-btn" id="generate-plan-btn">
                                <span class="spinner-border spinner-custom me-2" role="status" aria-hidden="true" style="display: none;"></span>
                                <i class="fas fa-magic me-2"></i>
                                <span class="btn-text">生成AI教案</span>
                            </button>
                        </div>
                    </form>

                    <!-- AI生成结果区域 -->
                    <div class="ai-result-area" id="ai-result-area" style="display: none;">
                        <div class="result-placeholder" id="result-placeholder">
                            <i class="fas fa-robot"></i>
                            <h5>AI正在为您生成教案...</h5>
                            <p>请稍候，这可能需要几秒钟时间</p>
                        </div>
                        <div id="generated-content" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-alt me-2 text-success"></i>AI生成的教案
                                </h5>
                                <button class="action-btn success-btn" id="save-plan-btn">
                                    <i class="fas fa-save me-2"></i>保存教案
                                </button>
                            </div>
                            <div id="plan-content" class="border rounded p-3" style="background: white; max-height: 500px; overflow-y: auto;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的教案列表 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-folder me-3 text-info"></i>我的教案库
                    </h5>
                </div>
                <div class="feature-card-body">
                    {% if teaching_plans %}
                        {% for plan in teaching_plans %}
                            <div class="teaching-plan-item">
                                <div class="plan-title">{{ plan.title }}</div>
                                <div class="plan-meta">
                                    <i class="fas fa-calendar-alt me-2"></i>{{ plan.created_at|date:"Y年m月d日 H:i" }}
                                </div>
                                <div class="plan-actions">
                                    <a href="{% url 'teacher_dashboard:prepare_design' %}?plan_id={{ plan.id }}#results-section" class="action-btn btn-sm-custom">
                                        <i class="fas fa-eye me-1"></i>查看详情
                                    </a>
                                    <form method="post" action="{% url 'teacher_dashboard:generate_questions' plan.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <button type="submit" class="action-btn success-btn btn-sm-custom">
                                            <i class="fas fa-cogs me-1"></i>生成题目
                                        </button>
                                    </form>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5>暂无教案</h5>
                            <p>上传教学资料并生成教案后，将显示在这里</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if selected_plan %}
            <!-- 教案详情区域 -->
            <div class="feature-card" id="results-section">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-file-alt me-3 text-success"></i>教案详情：{{ selected_plan.title }}
                    </h5>
                </div>
                <div class="feature-card-body">
                    <div class="border rounded p-3 mb-4" style="background: white;">
                        {{ selected_plan.content|linebreaksbr }}
                    </div>

                    {% if generated_questions %}
                        <h6><i class="fas fa-question-circle me-2"></i>基于此教案生成的题目</h6>
                        {% for question in generated_questions %}
                            <div class="border rounded p-3 mb-2" style="background: #f8f9fa;">
                                <strong>{{ question.get_question_type_display }}:</strong> {{ question.question_text }}
                                <br>
                                <small class="text-muted"><strong>答案:</strong> {{ question.correct_answer }}</small>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <p class="text-muted">此教案下暂无题目</p>
                            <form method="post" action="{% url 'teacher_dashboard:generate_questions' selected_plan.id %}">
                                {% csrf_token %}
                                <button type="submit" class="action-btn">
                                    <i class="fas fa-cogs me-2"></i>生成题目
                                </button>
                            </form>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 保存教案模态框 -->
    <div class="modal fade" id="savePlanModal" tabindex="-1" aria-labelledby="savePlanModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                    <h5 class="modal-title" id="savePlanModalLabel">
                        <i class="fas fa-save me-2"></i>保存教案
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <form id="save-plan-form">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="plan-title" class="form-label">教案标题</label>
                            <input type="text" class="form-control" id="plan-title" name="title" required
                                   style="border-radius: 10px; padding: 12px; border: 1px solid #e2e8f0;">
                        </div>
                        <input type="hidden" id="plan-content-hidden" name="content">
                    </form>
                </div>
                <div class="modal-footer" style="border-top: 1px solid #f1f5f9; border-radius: 0 0 20px 20px;">
                    <button type="button" class="action-btn secondary-btn" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="button" class="action-btn success-btn" id="confirm-save-btn">
                        <i class="fas fa-check me-2"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let generatedPlanContent = '';

        // 文件上传拖拽功能
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('teaching-document-input');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
            updateFileDisplay();
        });

        fileInput.addEventListener('change', updateFileDisplay);

        function updateFileDisplay() {
            const files = fileInput.files;
            if (files.length > 0) {
                const fileNames = Array.from(files).map(file => file.name).join(', ');
                uploadArea.querySelector('.upload-text').textContent = `已选择 ${files.length} 个文件`;
                uploadArea.querySelector('.upload-hint').textContent = fileNames;
            }
        }

        // 表单提交处理
        document.getElementById('teaching-document-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const files = fileInput.files;
            if (files.length === 0) {
                alert('请先选择文件');
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('teaching_document', files[i]);
            }

            // 添加CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            formData.append('csrfmiddlewaretoken', csrfToken);

            const generateBtn = document.getElementById('generate-plan-btn');
            const spinner = generateBtn.querySelector('.spinner-border');
            const btnText = generateBtn.querySelector('.btn-text');
            const resultArea = document.getElementById('ai-result-area');
            const placeholder = document.getElementById('result-placeholder');
            const generatedContent = document.getElementById('generated-content');

            // 显示加载状态
            spinner.style.display = 'inline-block';
            btnText.textContent = '生成中...';
            generateBtn.disabled = true;
            resultArea.style.display = 'block';
            placeholder.style.display = 'block';
            generatedContent.style.display = 'none';

            fetch('{% url "teacher_dashboard:prepare_design" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    generatedPlanContent = data.preparation_suggestions;
                    document.getElementById('plan-content').innerHTML = marked.parse(generatedPlanContent);
                    placeholder.style.display = 'none';
                    generatedContent.style.display = 'block';
                } else {
                    alert('生成失败: ' + data.message);
                    resultArea.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('生成失败，请重试');
                resultArea.style.display = 'none';
            })
            .finally(() => {
                spinner.style.display = 'none';
                btnText.textContent = '生成AI教案';
                generateBtn.disabled = false;
            });
        });

        // 保存教案
        document.getElementById('save-plan-btn').addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('savePlanModal'));
            modal.show();
        });

        document.getElementById('confirm-save-btn').addEventListener('click', function() {
            const title = document.getElementById('plan-title').value;
            if (!title.trim()) {
                alert('请输入教案标题');
                return;
            }

            const formData = new FormData();
            formData.append('title', title);
            formData.append('content', generatedPlanContent);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            fetch('{% url "teacher_dashboard:save_teaching_plan" %}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('教案保存成功！');
                    location.reload();
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败，请重试');
            });
        });
    </script>

    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</body>
</html>
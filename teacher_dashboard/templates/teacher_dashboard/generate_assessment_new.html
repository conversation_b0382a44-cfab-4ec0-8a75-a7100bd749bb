<!DOCTYPE html>
<html>
<head>
    <title>考核内容生成</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content-section {
            padding: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .feature-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .feature-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .feature-card-body {
            padding: 30px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
            color: white;
        }
        
        .action-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }
        
        .secondary-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }
        
        .secondary-btn:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        
        .success-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        
        .success-btn:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }
        
        .warning-btn {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }
        
        .warning-btn:hover {
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
        }
        
        .question-request-row {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .question-request-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .form-control-custom {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .form-control-custom:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }
        
        .form-label-custom {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .question-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .question-type-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .type-mc {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }
        
        .type-tf {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        
        .type-sa {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }
        
        .type-pg {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        .question-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .question-options {
            background: #f8fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .option-item {
            padding: 8px 0;
            color: #6c757d;
        }
        
        .correct-answer {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 15px;
            border-left: 4px solid #10b981;
        }
        
        .correct-answer-label {
            font-weight: 600;
            color: #065f46;
            margin-bottom: 5px;
        }
        
        .correct-answer-text {
            color: #047857;
        }
        
        .question-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .btn-sm-custom {
            padding: 8px 16px;
            font-size: 0.85rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .spinner-custom {
            width: 20px;
            height: 20px;
        }
        
        .progress-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .progress-title {
            font-weight: 600;
            color: #4f46e5;
            margin-bottom: 15px;
        }
        
        .progress-bar-custom {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-state i {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .empty-state h5 {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-cogs me-3"></i>考核内容生成
            </div>
            <div class="header-subtitle">
                智能生成题目 · 自定义考核 · 快速组卷
            </div>
        </div>
        
        <div class="content-section">
            <!-- 第一步：设置生成要求 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-cog me-3 text-primary"></i>第一步：设置生成要求
                    </h5>
                </div>
                <div class="feature-card-body">
                    <div id="question-request-container">
                        <!-- 初始题目请求 -->
                        <div class="question-request-row">
                            <div class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label class="form-label-custom">题目类型</label>
                                    <select class="form-control-custom form-select">
                                        <option value="MC">选择题</option>
                                        <option value="TF">判断题</option>
                                        <option value="SA">简答题</option>
                                        <option value="PG">编程题</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label-custom">数量</label>
                                    <input type="number" class="form-control-custom" value="3" min="1" max="10">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label-custom">学科</label>
                                    <input type="text" class="form-control-custom" placeholder="例如：数学">
                                </div>
                                <div class="col-md-2">
                                    <button class="action-btn secondary-btn btn-sm-custom w-100" onclick="removeQuestionRequest(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mb-4">
                        <button class="action-btn secondary-btn" onclick="addQuestionRequest()">
                            <i class="fas fa-plus me-2"></i>添加更多题型
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <button class="action-btn" id="generate-questions-btn">
                            <span class="spinner-border spinner-custom me-2" role="status" aria-hidden="true" style="display: none;"></span>
                            <i class="fas fa-magic me-2"></i>
                            <span class="btn-text">开始生成题目</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 生成进度 -->
            <div class="progress-section" id="progress-section" style="display: none;">
                <div class="progress-title">正在生成题目...</div>
                <div class="progress-bar-custom">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">请稍候，AI正在为您生成高质量的题目</small>
                </div>
            </div>
            
            <!-- 第二步：生成的题目 -->
            <div class="feature-card" id="generated-questions-section" style="display: none;">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-list-alt me-3 text-success"></i>第二步：生成的题目
                    </h5>
                </div>
                <div class="feature-card-body">
                    <div id="generated-questions-container">
                        <!-- 生成的题目将在这里显示 -->
                    </div>
                    
                    <div class="text-center mt-4" id="save-questions-section" style="display: none;">
                        <button class="action-btn success-btn" id="save-all-questions-btn">
                            <i class="fas fa-save me-2"></i>保存所有题目到题库
                        </button>
                        <button class="action-btn warning-btn ms-3" id="regenerate-btn">
                            <i class="fas fa-redo me-2"></i>重新生成
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div id="toast-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let questionRequestCount = 1;

        // 添加题目请求
        function addQuestionRequest() {
            questionRequestCount++;
            const container = document.getElementById('question-request-container');
            const newRow = document.createElement('div');
            newRow.className = 'question-request-row';
            newRow.innerHTML = `
                <div class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label class="form-label-custom">题目类型</label>
                        <select class="form-control-custom form-select">
                            <option value="MC">选择题</option>
                            <option value="TF">判断题</option>
                            <option value="SA">简答题</option>
                            <option value="PG">编程题</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label-custom">数量</label>
                        <input type="number" class="form-control-custom" value="3" min="1" max="10">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label-custom">学科</label>
                        <input type="text" class="form-control-custom" placeholder="例如：数学">
                    </div>
                    <div class="col-md-2">
                        <button class="action-btn secondary-btn btn-sm-custom w-100" onclick="removeQuestionRequest(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(newRow);
        }

        // 移除题目请求
        function removeQuestionRequest(button) {
            if (questionRequestCount > 1) {
                button.closest('.question-request-row').remove();
                questionRequestCount--;
            } else {
                showToast('至少需要保留一个题目类型', 'warning');
            }
        }

        // 生成题目
        document.getElementById('generate-questions-btn').addEventListener('click', function() {
            const btn = this;
            const spinner = btn.querySelector('.spinner-border');
            const btnText = btn.querySelector('.btn-text');
            const progressSection = document.getElementById('progress-section');
            const progressFill = document.getElementById('progress-fill');
            
            // 显示加载状态
            spinner.style.display = 'inline-block';
            btnText.textContent = '生成中...';
            btn.disabled = true;
            progressSection.style.display = 'block';
            
            // 模拟进度
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        // 隐藏进度，显示结果
                        progressSection.style.display = 'none';
                        showGeneratedQuestions();
                        
                        // 恢复按钮状态
                        spinner.style.display = 'none';
                        btnText.textContent = '开始生成题目';
                        btn.disabled = false;
                    }, 500);
                }
            }, 200);
        });

        // 显示生成的题目
        function showGeneratedQuestions() {
            const section = document.getElementById('generated-questions-section');
            const container = document.getElementById('generated-questions-container');
            const saveSection = document.getElementById('save-questions-section');
            
            // 模拟生成的题目
            const sampleQuestions = [
                {
                    type: 'MC',
                    typeName: '选择题',
                    question: '以下哪个是Python的正确语法？',
                    options: ['A. print "Hello World"', 'B. print("Hello World")', 'C. echo "Hello World"', 'D. console.log("Hello World")'],
                    answer: 'B'
                },
                {
                    type: 'TF',
                    typeName: '判断题',
                    question: 'Python是一种编译型语言。',
                    answer: '错误'
                },
                {
                    type: 'SA',
                    typeName: '简答题',
                    question: '请简述Python中列表和元组的区别。',
                    answer: '列表是可变的，元组是不可变的；列表使用方括号，元组使用圆括号。'
                }
            ];
            
            container.innerHTML = '';
            sampleQuestions.forEach((q, index) => {
                const questionCard = createQuestionCard(q, index);
                container.appendChild(questionCard);
            });
            
            section.style.display = 'block';
            saveSection.style.display = 'block';
            
            showToast('成功生成 ' + sampleQuestions.length + ' 道题目！', 'success');
        }

        // 创建题目卡片
        function createQuestionCard(question, index) {
            const card = document.createElement('div');
            card.className = 'question-card';
            
            let optionsHtml = '';
            if (question.options) {
                optionsHtml = `
                    <div class="question-options">
                        <strong>选项：</strong>
                        ${question.options.map(opt => `<div class="option-item">${opt}</div>`).join('')}
                    </div>
                `;
            }
            
            card.innerHTML = `
                <div class="question-type-badge type-${question.type.toLowerCase()}">
                    ${question.typeName}
                </div>
                <div class="question-text">${question.question}</div>
                ${optionsHtml}
                <div class="correct-answer">
                    <div class="correct-answer-label">正确答案：</div>
                    <div class="correct-answer-text">${question.answer}</div>
                </div>
                <div class="question-actions">
                    <button class="action-btn btn-sm-custom secondary-btn" onclick="editQuestion(${index})">
                        <i class="fas fa-edit me-1"></i>编辑
                    </button>
                    <button class="action-btn btn-sm-custom secondary-btn" onclick="deleteQuestion(${index})">
                        <i class="fas fa-trash me-1"></i>删除
                    </button>
                </div>
            `;
            
            return card;
        }

        // 编辑题目
        function editQuestion(index) {
            showToast('编辑功能开发中...', 'info');
        }

        // 删除题目
        function deleteQuestion(index) {
            if (confirm('确定要删除这道题目吗？')) {
                showToast('题目已删除', 'success');
                // 这里可以添加删除逻辑
            }
        }

        // 保存所有题目
        document.getElementById('save-all-questions-btn').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';
            
            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-save me-2"></i>保存所有题目到题库';
                showToast('所有题目已成功保存到题库！', 'success');
            }, 2000);
        });

        // 重新生成
        document.getElementById('regenerate-btn').addEventListener('click', function() {
            if (confirm('确定要重新生成题目吗？当前的题目将被替换。')) {
                document.getElementById('generate-questions-btn').click();
            }
        });

        // Toast 提示
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <title>课件资源管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content-section {
            padding: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .feature-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .feature-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .feature-card-body {
            padding: 30px;
        }
        
        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
        }
        
        .upload-area.dragover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }
        
        .upload-icon {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.1rem;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .file-input {
            display: none;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
            color: white;
        }
        
        .action-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }
        
        .secondary-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }
        
        .secondary-btn:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        
        .success-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        
        .success-btn:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }
        
        .courseware-item {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .courseware-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .courseware-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .courseware-meta {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
        }
        
        .meta-item i {
            margin-right: 5px;
            color: #4f46e5;
        }
        
        .courseware-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-sm-custom {
            padding: 8px 16px;
            font-size: 0.85rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .subject-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            color: #4f46e5;
        }
        
        .form-control-custom {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .form-control-custom:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }
        
        .form-label-custom {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .spinner-custom {
            width: 20px;
            height: 20px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-state i {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .empty-state h5 {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-book-open me-3"></i>课件资源管理
            </div>
            <div class="header-subtitle">
                上传课件 · 管理资源 · 分享教学内容
            </div>
        </div>
        
        <div class="content-section">
            <!-- 上传新课件 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-upload me-3 text-primary"></i>上传新课件
                    </h5>
                </div>
                <div class="feature-card-body">
                    <form id="courseware-upload-form" method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="title" class="form-label-custom">课件标题</label>
                                <input type="text" class="form-control form-control-custom" id="title" name="title" placeholder="例如：高一数学上册第一单元" required>
                            </div>
                            <div class="col-md-6">
                                <label for="subject" class="form-label-custom">学科</label>
                                <input type="text" class="form-control form-control-custom" id="subject" name="subject" placeholder="例如：数学" required>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label for="description" class="form-label-custom">描述</label>
                            <textarea class="form-control form-control-custom" id="description" name="description" rows="3" placeholder="简要描述课件内容..."></textarea>
                        </div>
                        <div class="upload-area mb-4" onclick="document.getElementById('file').click()">
                            <div class="upload-icon">
                                <i class="fas fa-file-upload"></i>
                            </div>
                            <div class="upload-text">点击上传或拖拽文件到此处</div>
                            <div class="upload-hint">支持 .ppt, .pptx, .pdf, .doc, .docx 等格式</div>
                            <input type="file" class="file-input" id="file" name="file" required>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="action-btn" id="upload-btn">
                                <span class="spinner-border spinner-custom me-2" role="status" aria-hidden="true" style="display: none;"></span>
                                <i class="fas fa-upload me-2"></i>
                                <span class="btn-text">上传课件</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 我的课件列表 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-list-alt me-3 text-success"></i>我的课件列表
                    </h5>
                </div>
                <div class="feature-card-body">
                    {% if coursewares %}
                        <div class="row">
                            {% for courseware in coursewares %}
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="courseware-item">
                                        <div class="courseware-title">{{ courseware.title }}</div>
                                        <div class="courseware-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                {{ courseware.uploaded_at|date:"Y年m月d日" }}
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-book"></i>
                                                <span class="subject-badge">{{ courseware.subject }}</span>
                                            </div>
                                        </div>
                                        <div class="courseware-actions">
                                            <a href="{{ courseware.file.url }}" class="action-btn btn-sm-custom" target="_blank">
                                                <i class="fas fa-download me-1"></i>下载
                                            </a>
                                            <button type="button" class="action-btn secondary-btn btn-sm-custom" onclick="if(confirm('确定要删除这个课件吗？')) alert('删除功能开发中...');">
                                                <i class="fas fa-trash me-1"></i>删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-book"></i>
                            <h5>暂无课件</h5>
                            <p>上传您的第一个课件，开始管理您的教学资源</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 文件上传拖拽功能
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('file');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
            updateFileDisplay();
        });

        fileInput.addEventListener('change', updateFileDisplay);

        function updateFileDisplay() {
            const files = fileInput.files;
            if (files.length > 0) {
                uploadArea.querySelector('.upload-text').textContent = `已选择: ${files[0].name}`;
                uploadArea.querySelector('.upload-hint').textContent = `文件大小: ${formatFileSize(files[0].size)}`;
            }
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' bytes';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
            else return (bytes / 1048576).toFixed(1) + ' MB';
        }

        // 表单提交处理
        document.getElementById('courseware-upload-form').addEventListener('submit', function(e) {
            const uploadBtn = document.getElementById('upload-btn');
            const spinner = uploadBtn.querySelector('.spinner-border');
            const btnText = uploadBtn.querySelector('.btn-text');
            
            spinner.style.display = 'inline-block';
            btnText.textContent = '上传中...';
            uploadBtn.disabled = true;
        });
    </script>
</body>
</html>

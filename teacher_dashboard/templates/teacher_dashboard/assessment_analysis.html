<!DOCTYPE html>
<html>
<head>
    <title>考情分析: {{ assessment.title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content-section {
            padding: 40px;
        }

        .stat-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
            border: none;
            transition: all 0.3s ease;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .analysis-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .analysis-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
        }

        .analysis-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .analysis-card-body {
            padding: 30px;
        }

        .nav-tabs {
            border: none;
            background: #f8fafc;
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 30px;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px;
            color: #6c757d;
            font-weight: 500;
            padding: 12px 25px;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }

        .tab-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .student-list {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .student-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .student-item:last-child {
            border-bottom: none;
        }

        .student-name {
            font-weight: 500;
            color: #1e293b;
        }

        .student-score {
            font-weight: 600;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .score-excellent {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .score-good {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .score-average {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .score-poor {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .back-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            color: white;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
            color: white;
            text-decoration: none;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .table thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: none;
            font-weight: 600;
            color: #1e293b;
            padding: 20px;
        }

        .table tbody td {
            padding: 20px;
            border-color: #f1f5f9;
            vertical-align: middle;
        }

        .wrong-question-item {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 10px 10px 0;
        }

        .wrong-question-text {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .wrong-question-stats {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>

    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                考情分析: {{ assessment.title }}
            </div>
            <div class="header-subtitle">
                深度分析学生答题情况 · 精准定位学习问题 · 优化教学策略
            </div>
        </div>

        <div class="content-section">
            <!-- 返回按钮 -->
            <a href="{% url 'teacher_dashboard:assessment_list' %}" class="back-btn">
                <i class="fas fa-arrow-left me-2"></i>返回考核列表
            </a>

            <!-- 概览数据 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users text-primary"></i>
                        </div>
                        <div class="stat-value text-primary">{{ submitted_students.count }}/{{ unsubmitted_students.count|add:submitted_students.count }}</div>
                        <div class="stat-label">提交情况</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line text-success"></i>
                        </div>
                        <div class="stat-value text-success">{{ overall_accuracy|floatformat:1 }}%</div>
                        <div class="stat-label">整体正确率</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        </div>
                        <div class="stat-label mb-3">高频错题</div>
                        {% if top_wrong_questions %}
                            <div class="text-start">
                                {% for stat in top_wrong_questions %}
                                    <div class="wrong-question-item">
                                        <div class="wrong-question-text">
                                            {{ stat.question.question_text|truncatechars:40 }}
                                        </div>
                                        <div class="wrong-question-stats">
                                            <i class="fas fa-times-circle me-1"></i>正确率 {{ stat.accuracy|floatformat:1 }}%
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-muted">暂无错题数据</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 详细数据切换标签 -->
            <ul class="nav nav-tabs" id="analysisTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats-content" type="button" role="tab" aria-controls="stats-content" aria-selected="true">
                        <i class="fas fa-chart-bar me-2"></i>题目统计
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details-content" type="button" role="tab" aria-controls="details-content" aria-selected="false">
                        <i class="fas fa-users me-2"></i>学生详情
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="status-tab" data-bs-toggle="tab" data-bs-target="#status-content" type="button" role="tab" aria-controls="status-content" aria-selected="false">
                        <i class="fas fa-clipboard-check me-2"></i>提交状态
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="analysisTabContent">
                <!-- 题目统计内容 -->
                <div class="tab-pane fade show active" id="stats-content" role="tabpanel" aria-labelledby="stats-tab">
                    <div class="chart-container">
                        <canvas id="accuracyChart"></canvas>
                    </div>
                </div>

                <!-- 学生详情内容 -->
                <div class="tab-pane fade" id="details-content" role="tabpanel" aria-labelledby="details-tab">
                    <div class="accordion" id="studentDetailsAccordion">
                        {% for detail in student_details %}
                        <div class="accordion-item mb-3" style="border-radius: 15px; overflow: hidden; border: none; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                            <h2 class="accordion-header" id="heading-{{ forloop.counter }}">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{{ forloop.counter }}" aria-expanded="false" aria-controls="collapse-{{ forloop.counter }}" style="padding: 20px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                                    <div class="w-100 d-flex justify-content-between align-items-center pe-2">
                                        <span>
                                            <div class="d-flex align-items-center">
                                                <div class="student-avatar me-2" style="width: 40px; height: 40px; background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600;">
                                                    {{ detail.student.username|first|upper }}
                                                </div>
                                                <div>{{ detail.student.username }}</div>
                                            </div>
                                        </span>
                                        {% if detail.has_submitted %}
                                            <span class="student-score
                                                {% if detail.score >= 80 %}score-excellent
                                                {% elif detail.score >= 60 %}score-good
                                                {% elif detail.score >= 40 %}score-average
                                                {% else %}score-poor{% endif %}">
                                                {{ detail.score|floatformat:1 }}%
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">未提交</span>
                                        {% endif %}
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse-{{ forloop.counter }}" class="accordion-collapse collapse" aria-labelledby="heading-{{ forloop.counter }}" data-bs-parent="#studentDetailsAccordion">
                                <div class="accordion-body" style="padding: 25px;">
                                    {% if detail.has_submitted %}
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                                <span>提交时间: {% if detail.submission_time %}{{ detail.submission_time|date:"Y-m-d H:i" }}{% else %}未知{% endif %}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-clock me-2 text-primary"></i>
                                                <span>用时: {% if detail.time_spent %}{{ detail.time_spent }}{% else %}未知{% endif %}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="student-answers">
                                        {% for r_detail in detail.responses_details %}
                                        <div class="answer-item mb-3 p-3" style="background: #f8fafc; border-radius: 10px;">
                                            <div class="question-text mb-2">
                                                <strong>题目 {{ forloop.counter }}: </strong>{{ r_detail.question.question_text }}
                                            </div>
                                            <div class="d-flex flex-wrap">
                                                <div class="me-4 mb-2">
                                                    <div class="answer-label mb-1">学生答案:</div>
                                                    <div class="answer-badge {% if r_detail.is_correct %}bg-success-light{% else %}bg-danger-light{% endif %}" style="padding: 8px 15px; border-radius: 20px; display: inline-block; font-size: 0.9rem;">
                                                        {{ r_detail.student_answer|default:"未作答" }}
                                                    </div>
                                                </div>
                                                {% if not r_detail.is_correct %}
                                                <div>
                                                    <div class="answer-label mb-1">正确答案:</div>
                                                    <div class="answer-badge bg-info-light" style="padding: 8px 15px; border-radius: 20px; display: inline-block; font-size: 0.9rem;">
                                                        {{ r_detail.question.answer }}
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-exclamation-circle text-muted mb-3" style="font-size: 2rem;"></i>
                                        <p class="mb-0">该学生未提交本次考核</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 提交状态内容 -->
                <div class="tab-pane fade" id="status-content" role="tabpanel" aria-labelledby="status-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="analysis-card">
                                <div class="analysis-card-header">
                                    <h5 class="analysis-card-title">
                                        <i class="fas fa-check-circle text-success me-2"></i>已提交 ({{ submitted_students.count }})
                                    </h5>
                                </div>
                                <div class="analysis-card-body">
                                    <div class="student-list">
                                        {% for detail in student_details %}
                                            {% if detail.has_submitted %}
                                            <div class="student-item">
                                                <div class="d-flex align-items-center flex-grow-1">
                                                    <div class="student-avatar me-3" style="width: 35px; height: 35px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem;">
                                                        {{ detail.student.username|first|upper }}
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="student-name">{{ detail.student.username }}</div>
                                                        <div class="text-muted small">
                                                            <i class="fas fa-clock me-1"></i>
                                                            {% if detail.submission_time %}{{ detail.submission_time|date:"m-d H:i" }}{% else %}未知{% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="student-score
                                                    {% if detail.score >= 80 %}score-excellent
                                                    {% elif detail.score >= 60 %}score-good
                                                    {% elif detail.score >= 40 %}score-average
                                                    {% else %}score-poor{% endif %}">
                                                    {{ detail.score|floatformat:1 }}%
                                                </div>
                                            </div>
                                            {% endif %}
                                        {% empty %}
                                            <div class="text-center py-4">
                                                <i class="fas fa-inbox text-muted mb-2" style="font-size: 2rem;"></i>
                                                <p class="mb-0">暂无学生提交</p>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="analysis-card">
                                <div class="analysis-card-header">
                                    <h5 class="analysis-card-title">
                                        <i class="fas fa-exclamation-circle text-warning me-2"></i>未提交 ({{ unsubmitted_students.count }})
                                    </h5>
                                </div>
                                <div class="analysis-card-body">
                                    <div class="student-list">
                                        {% for student in unsubmitted_students %}
                                        <div class="student-item">
                                            <div class="d-flex align-items-center">
                                                <div class="student-avatar me-3" style="width: 35px; height: 35px; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem;">
                                                    {{ student.username|first|upper }}
                                                </div>
                                                <div class="student-name">{{ student.username }}</div>
                                            </div>
                                            <div class="badge bg-warning text-dark">
                                                <i class="fas fa-clock me-1"></i>待提交
                                            </div>
                                        </div>
                                        {% empty %}
                                            <div class="text-center py-4">
                                                <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                                                <p class="mb-0">所有学生都已提交</p>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% block modals %}{% endblock %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('accuracyChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [{% for stat in question_stats %}"题目 {{ forloop.counter }}",{% endfor %}],
                    datasets: [{
                        label: '正确率 (%)',
                        data: [{% for stat in question_stats %}{{ stat.accuracy }},{% endfor %}],
                        backgroundColor: 'rgba(79, 70, 229, 0.8)',
                        borderColor: 'rgba(79, 70, 229, 1)',
                        borderWidth: 2,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            padding: 12,
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 14
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
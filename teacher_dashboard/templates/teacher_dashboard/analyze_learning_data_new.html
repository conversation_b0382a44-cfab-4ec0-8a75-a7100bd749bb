<!DOCTYPE html>
<html>
<head>
    <title>学情数据分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content-section {
            padding: 40px;
        }
        
        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: none;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-card .stat-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
        }
        
        .stat-card .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }
        
        .stat-card.primary .stat-icon { color: #4f46e5; }
        .stat-card.success .stat-icon { color: #10b981; }
        .stat-card.warning .stat-icon { color: #f59e0b; }
        .stat-card.info .stat-icon { color: #06b6d4; }
        
        .analysis-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
        }
        
        .analysis-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .analysis-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .analysis-card-title i {
            margin-right: 10px;
            color: #4f46e5;
        }
        
        .analysis-card-body {
            padding: 30px;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .chart-container-small {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .student-rank-item {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }
        
        .student-rank-item:hover {
            background: #e2e8f0;
            transform: translateX(5px);
        }
        
        .student-info {
            display: flex;
            align-items: center;
        }
        
        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 15px;
        }
        
        .student-name {
            font-weight: 600;
            color: #1e293b;
        }
        
        .accuracy-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .accuracy-excellent {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        
        .accuracy-good {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }
        
        .accuracy-poor {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        .assessment-table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .assessment-table th {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 20px;
        }
        
        .assessment-table td {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
        }
        
        .assessment-table tr:last-child td {
            border-bottom: none;
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 10px;
            background: #e2e8f0;
            overflow: hidden;
        }
        
        .progress-bar-custom {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: width 0.6s ease;
        }
        
        .knowledge-point-item {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #4f46e5;
        }
        
        .knowledge-point-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .mastery-rate {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4f46e5;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 30px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .wrong-question-item {
            background: #fef2f2;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 5px solid #ef4444;
        }
        
        .wrong-question-text {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .error-count {
            color: #ef4444;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-chart-line me-3"></i>学情数据分析
            </div>
            <div class="header-subtitle">
                智能分析 · 数据洞察 · 教学优化
            </div>
        </div>
        
        <div class="content-section">
            <!-- 综合数据统计 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-number">{{ question_count }}</div>
                        <div class="stat-label">题库题目总数</div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-number">{{ assessment_count }}</div>
                        <div class="stat-label">已发布考核数</div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">{{ covered_students_count }}</div>
                        <div class="stat-label">覆盖学生数</div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="stat-number">{{ overall_mastery }}</div>
                        <div class="stat-label">总体掌握率</div>
                    </div>
                </div>
            </div>

            <!-- 图表分析区域 -->
            <div class="row mb-4">
                <div class="col-xl-8">
                    <div class="analysis-card">
                        <div class="analysis-card-header">
                            <h5 class="analysis-card-title">
                                <i class="fas fa-line-chart"></i>近期考核正确率趋势
                            </h5>
                        </div>
                        <div class="analysis-card-body">
                            <div class="chart-container">
                                <canvas id="accuracyTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4">
                    <div class="analysis-card">
                        <div class="analysis-card-header">
                            <h5 class="analysis-card-title">
                                <i class="fas fa-pie-chart"></i>题库题型分布
                            </h5>
                        </div>
                        <div class="analysis-card-body">
                            <div class="chart-container-small">
                                <canvas id="questionTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高频错题分析 -->
            <div class="analysis-card mb-4">
                <div class="analysis-card-header">
                    <h5 class="analysis-card-title">
                        <i class="fas fa-exclamation-triangle"></i>高频错题 Top 5
                    </h5>
                </div>
                <div class="analysis-card-body">
                    {% if top_wrong_questions %}
                        {% for wrong in top_wrong_questions %}
                        <div class="wrong-question-item">
                            <div class="wrong-question-text">{{ wrong.question__question_text|truncatechars:100 }}</div>
                            <div class="error-count">错误次数: {{ wrong.error_count }}</div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <h5>太棒了！</h5>
                            <p>暂无高频错题，学生表现优秀！</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- 知识点掌握情况 -->
            <div class="analysis-card mb-4">
                <div class="analysis-card-header">
                    <h5 class="analysis-card-title">
                        <i class="fas fa-brain"></i>知识点掌握情况
                    </h5>
                </div>
                <div class="analysis-card-body">
                    {% if knowledge_points_analysis %}
                        <div class="row">
                            {% for kp in knowledge_points_analysis %}
                            <div class="col-md-6 mb-3">
                                <div class="knowledge-point-item">
                                    <div class="knowledge-point-title">{{ kp.subject }}</div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="mastery-rate">{{ kp.mastery_rate }}%</div>
                                        <div class="text-muted">{{ kp.total_questions }} 题</div>
                                    </div>
                                    <div class="progress-custom mt-2">
                                        <div class="progress-bar-custom" style="width: {{ kp.mastery_rate }}%"></div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-book-open"></i>
                            <h5>暂无数据</h5>
                            <p>还没有足够的考核数据进行知识点分析</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- 学生表现排行 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="analysis-card">
                        <div class="analysis-card-header">
                            <h5 class="analysis-card-title">
                                <i class="fas fa-star"></i>学生光荣榜 (Top 5)
                            </h5>
                        </div>
                        <div class="analysis-card-body">
                            {% if top_students %}
                                {% for student_perf in top_students %}
                                <div class="student-rank-item">
                                    <div class="student-info">
                                        <div class="student-avatar">
                                            {{ student_perf.student.username|first|upper }}
                                        </div>
                                        <div class="student-name">{{ student_perf.student.username }}</div>
                                    </div>
                                    <div class="accuracy-badge accuracy-excellent">
                                        {{ student_perf.avg_accuracy|floatformat:1 }}%
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="empty-state">
                                    <i class="fas fa-users"></i>
                                    <h5>暂无数据</h5>
                                    <p>暂无学生参与考核</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="analysis-card">
                        <div class="analysis-card-header">
                            <h5 class="analysis-card-title">
                                <i class="fas fa-hand-holding-heart"></i>需关注学生 (Bottom 5)
                            </h5>
                        </div>
                        <div class="analysis-card-body">
                            {% if bottom_students %}
                                {% for student_perf in bottom_students %}
                                <div class="student-rank-item">
                                    <div class="student-info">
                                        <div class="student-avatar">
                                            {{ student_perf.student.username|first|upper }}
                                        </div>
                                        <div class="student-name">{{ student_perf.student.username }}</div>
                                    </div>
                                    <div class="accuracy-badge {% if student_perf.avg_accuracy >= 60 %}accuracy-good{% else %}accuracy-poor{% endif %}">
                                        {{ student_perf.avg_accuracy|floatformat:1 }}%
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="empty-state">
                                    <i class="fas fa-users"></i>
                                    <h5>暂无数据</h5>
                                    <p>暂无学生参与考核</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 近期考核分析 -->
            <div class="analysis-card mb-4">
                <div class="analysis-card-header">
                    <h5 class="analysis-card-title">
                        <i class="fas fa-file-alt"></i>近期考核分析 (最近10次)
                    </h5>
                </div>
                <div class="analysis-card-body">
                    {% if assessments_summary %}
                        <div class="table-responsive">
                            <table class="table assessment-table">
                                <thead>
                                    <tr>
                                        <th>考核标题</th>
                                        <th>提交人数</th>
                                        <th>平均正确率</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for summary in assessments_summary %}
                                    <tr>
                                        <td>
                                            <strong>{{ summary.assessment.title }}</strong>
                                            <br>
                                            <small class="text-muted">{{ summary.assessment.created_at|date:"Y-m-d" }}</small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">{{ summary.submitted_count }}/{{ summary.total_students }}</span>
                                                <div class="progress-custom" style="width: 100px;">
                                                    {% widthratio summary.submitted_count summary.total_students 100 as progress_width %}
                                                    <div class="progress-bar-custom" style="width: {{ progress_width }}%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="accuracy-badge {% if summary.average_score >= 80 %}accuracy-excellent{% elif summary.average_score >= 60 %}accuracy-good{% else %}accuracy-poor{% endif %}">
                                                {{ summary.average_score }}%
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'teacher_dashboard:assessment_analysis' summary.assessment.id %}"
                                               class="btn btn-sm btn-outline-primary">查看详情</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-clipboard-list"></i>
                            <h5>暂无考核</h5>
                            <p>还没有发布任何考核</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- 详细统计分析 -->
            <div class="row mb-4">
                <div class="col-xl-6">
                    <div class="analysis-card">
                        <div class="analysis-card-header">
                            <h5 class="analysis-card-title">
                                <i class="fas fa-chart-bar"></i>学生成绩分布
                            </h5>
                        </div>
                        <div class="analysis-card-body">
                            <div class="chart-container-small">
                                <canvas id="scoreDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6">
                    <div class="analysis-card">
                        <div class="analysis-card-header">
                            <h5 class="analysis-card-title">
                                <i class="fas fa-clock"></i>答题时间分析
                            </h5>
                        </div>
                        <div class="analysis-card-body">
                            <div class="chart-container-small">
                                <canvas id="timeAnalysisChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 教学建议 -->
            <div class="analysis-card mb-4">
                <div class="analysis-card-header">
                    <h5 class="analysis-card-title">
                        <i class="fas fa-lightbulb"></i>AI教学建议与统计总结
                    </h5>
                </div>
                <div class="analysis-card-body">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <div class="knowledge-point-item" style="border-left-color: #4f46e5;">
                                <div class="knowledge-point-title">
                                    <i class="fas fa-chart-line me-2"></i>整体学情统计分析
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-3 text-center">
                                        <div class="fs-2 fw-bold text-primary">{{ total_student_count }}</div>
                                        <div class="text-muted">参与学生数</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="fs-2 fw-bold text-success">{{ assessment_count }}</div>
                                        <div class="text-muted">发布考核数</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="fs-2 fw-bold text-warning">{{ overall_avg_accuracy }}%</div>
                                        <div class="text-muted">平均正确率</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="fs-2 fw-bold text-info">{{ question_count }}</div>
                                        <div class="text-muted">题库总量</div>
                                    </div>
                                </div>
                                <hr class="my-3">
                                <p class="mb-0">
                                    {% if overall_avg_accuracy >= 80 %}
                                        <i class="fas fa-thumbs-up text-success me-2"></i>
                                        学生整体掌握情况良好，正确率达到{{ overall_avg_accuracy }}%。建议可以适当增加难度，挑战学生能力。
                                    {% elif overall_avg_accuracy >= 60 %}
                                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                        学生整体掌握情况一般，正确率为{{ overall_avg_accuracy }}%。建议针对错误较多的题目进行重点讲解。
                                    {% else %}
                                        <i class="fas fa-exclamation-circle text-danger me-2"></i>
                                        学生整体掌握情况不佳，正确率仅为{{ overall_avg_accuracy }}%。建议重新梳理教学内容，加强基础知识点讲解。
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="knowledge-point-item" style="border-left-color: #10b981;">
                                <div class="knowledge-point-title">
                                    <i class="fas fa-trophy me-2"></i>优势领域分析
                                </div>
                                <ul class="mb-0">
                                    {% for kp in knowledge_points_analysis %}
                                        {% if kp.mastery_rate >= 80 %}
                                            <li class="mb-2">
                                                <strong>{{ kp.subject }}</strong>: 掌握率{{ kp.mastery_rate }}%
                                                <div class="progress-custom mt-1" style="height: 6px;">
                                                    <div class="progress-bar-custom" style="width: {{ kp.mastery_rate }}%; background: linear-gradient(135deg, #10b981 0%, #059669 100%);"></div>
                                                </div>
                                            </li>
                                        {% endif %}
                                    {% empty %}
                                        <li class="text-muted">暂无明显优势领域，需要全面加强</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="knowledge-point-item" style="border-left-color: #ef4444;">
                                <div class="knowledge-point-title">
                                    <i class="fas fa-exclamation-triangle me-2"></i>薄弱环节分析
                                </div>
                                <ul class="mb-0">
                                    {% for kp in knowledge_points_analysis %}
                                        {% if kp.mastery_rate < 60 %}
                                            <li class="mb-2">
                                                <strong>{{ kp.subject }}</strong>: 掌握率仅{{ kp.mastery_rate }}%
                                                <div class="progress-custom mt-1" style="height: 6px;">
                                                    <div class="progress-bar-custom" style="width: {{ kp.mastery_rate }}%; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);"></div>
                                                </div>
                                            </li>
                                        {% endif %}
                                    {% empty %}
                                        <li class="text-muted">暂无明显薄弱环节，可以适当提高教学难度</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-12 mt-4">
                            <div class="knowledge-point-item" style="border-left-color: #6366f1;">
                                <div class="knowledge-point-title">
                                    <i class="fas fa-chalkboard-teacher me-2"></i>个性化教学建议
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6 class="text-primary">针对优秀学生</h6>
                                        <ul class="small">
                                            <li>提供更具挑战性的题目</li>
                                            <li>安排同伴辅导任务</li>
                                            <li>拓展深度学习内容</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="text-warning">针对中等学生</h6>
                                        <ul class="small">
                                            <li>加强基础知识巩固</li>
                                            <li>提供更多练习机会</li>
                                            <li>个别化指导答疑</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="text-danger">针对待提高学生</h6>
                                        <ul class="small">
                                            <li>重点关注基础概念</li>
                                            <li>增加一对一辅导</li>
                                            <li>制定个性化学习计划</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 近期考核正确率趋势图
            const accuracyTrendCtx = document.getElementById('accuracyTrendChart').getContext('2d');

            try {
                const accuracyLabels = JSON.parse('{{ assessment_accuracy_labels_json|safe }}');
                const accuracyData = JSON.parse('{{ assessment_accuracy_data_json|safe }}');

                new Chart(accuracyTrendCtx, {
                    type: 'line',
                    data: {
                        labels: accuracyLabels,
                        datasets: [{
                            label: '正确率 (%)',
                            data: accuracyData,
                            backgroundColor: 'rgba(111, 66, 193, 0.2)',
                            borderColor: 'rgba(111, 66, 193, 1)',
                            borderWidth: 3,
                            pointBackgroundColor: 'rgba(111, 66, 193, 1)',
                            pointBorderColor: '#fff',
                            pointRadius: 6,
                            pointHoverRadius: 8,
                            tension: 0.3,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                padding: 12,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 14
                                },
                                displayColors: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100,
                                ticks: {
                                    callback: function(value) {
                                        return value + '%';
                                    }
                                }
                            }
                        }
                    }
                });
            } catch (e) {
                console.error('Error creating accuracy trend chart:', e);
                document.getElementById('accuracyTrendChart').parentNode.innerHTML =
                    '<div class="empty-state"><i class="fas fa-chart-line"></i><h5>暂无数据</h5><p>还没有足够的考核数据生成趋势图</p></div>';
            }

            // 题型分布饼图
            const questionTypeCtx = document.getElementById('questionTypeChart').getContext('2d');

            try {
                const questionTypeData = JSON.parse('{{ question_type_data_json|safe }}');
                const labels = Object.keys(questionTypeData);
                const data = Object.values(questionTypeData);

                new Chart(questionTypeCtx, {
                    type: 'doughnut',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: [
                                'rgba(111, 66, 193, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(239, 68, 68, 0.8)'
                            ],
                            borderColor: [
                                'rgba(111, 66, 193, 1)',
                                'rgba(16, 185, 129, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(239, 68, 68, 1)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    font: {
                                        size: 14
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                padding: 12,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 14
                                }
                            }
                        },
                        cutout: '60%'
                    }
                });
            } catch (e) {
                console.error('Error creating question type chart:', e);
                document.getElementById('questionTypeChart').parentNode.innerHTML =
                    '<div class="empty-state"><i class="fas fa-chart-pie"></i><h5>暂无数据</h5><p>题库中还没有足够的题目</p></div>';
            }

            // 学生成绩分布图
            const scoreDistributionCtx = document.getElementById('scoreDistributionChart').getContext('2d');

            try {
                // 模拟成绩分布数据
                const scoreRanges = ['0-20%', '21-40%', '41-60%', '61-80%', '81-100%'];
                const scoreDistribution = [2, 5, 8, 12, 15]; // 示例数据

                new Chart(scoreDistributionCtx, {
                    type: 'bar',
                    data: {
                        labels: scoreRanges,
                        datasets: [{
                            label: '学生人数',
                            data: scoreDistribution,
                            backgroundColor: [
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(34, 197, 94, 0.8)'
                            ],
                            borderColor: [
                                'rgba(239, 68, 68, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(59, 130, 246, 1)',
                                'rgba(16, 185, 129, 1)',
                                'rgba(34, 197, 94, 1)'
                            ],
                            borderWidth: 2,
                            borderRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                padding: 12,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 14
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            } catch (e) {
                console.error('Error creating score distribution chart:', e);
                document.getElementById('scoreDistributionChart').parentNode.innerHTML =
                    '<div class="empty-state"><i class="fas fa-chart-bar"></i><h5>暂无数据</h5><p>还没有足够的成绩数据</p></div>';
            }

            // 答题时间分析图
            const timeAnalysisCtx = document.getElementById('timeAnalysisChart').getContext('2d');

            try {
                // 模拟答题时间数据
                const timeLabels = ['< 5分钟', '5-10分钟', '10-15分钟', '15-20分钟', '> 20分钟'];
                const timeData = [3, 8, 15, 10, 6]; // 示例数据

                new Chart(timeAnalysisCtx, {
                    type: 'doughnut',
                    data: {
                        labels: timeLabels,
                        datasets: [{
                            data: timeData,
                            backgroundColor: [
                                'rgba(99, 102, 241, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(156, 163, 175, 0.8)'
                            ],
                            borderColor: [
                                'rgba(99, 102, 241, 1)',
                                'rgba(16, 185, 129, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(239, 68, 68, 1)',
                                'rgba(156, 163, 175, 1)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                padding: 12,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 14
                                }
                            }
                        },
                        cutout: '50%'
                    }
                });
            } catch (e) {
                console.error('Error creating time analysis chart:', e);
                document.getElementById('timeAnalysisChart').parentNode.innerHTML =
                    '<div class="empty-state"><i class="fas fa-clock"></i><h5>暂无数据</h5><p>还没有足够的时间数据</p></div>';
            }
        });
    </script>
</body>
</html>

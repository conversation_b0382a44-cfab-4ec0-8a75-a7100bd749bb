<!DOCTYPE html>
<html>
<head>
    <title>题库管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content-section {
            padding: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .feature-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .feature-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .feature-card-body {
            padding: 30px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
            color: white;
        }
        
        .action-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }
        
        .secondary-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }
        
        .secondary-btn:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        
        .success-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        
        .success-btn:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }
        
        .question-item {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .question-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .question-item.selected {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
        }
        
        .question-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .question-type-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .type-mc {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }
        
        .type-tf {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        
        .type-sa {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }
        
        .type-pg {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        .question-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .question-options {
            background: #f8fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .option-item {
            padding: 8px 0;
            color: #6c757d;
        }
        
        .correct-answer {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 15px;
            border-left: 4px solid #10b981;
        }
        
        .correct-answer-label {
            font-weight: 600;
            color: #065f46;
            margin-bottom: 5px;
        }
        
        .correct-answer-text {
            color: #047857;
        }
        
        .question-meta {
            display: flex;
            justify-content: between;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
        }
        
        .meta-item i {
            margin-right: 5px;
            color: #4f46e5;
        }
        
        .question-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm-custom {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 15px;
            font-weight: 500;
        }
        
        .select-all-section {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #e2e8f0;
        }
        
        .form-check-custom {
            display: flex;
            align-items: center;
            font-weight: 500;
        }
        
        .form-check-custom input {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-state i {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .empty-state h5 {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            color: #6c757d;
        }
        
        .stats-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #4f46e5;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-database me-3"></i>题库管理
            </div>
            <div class="header-subtitle">
                管理题目 · 组织考核 · 智能分析
            </div>
        </div>
        
        <div class="content-section">
            <!-- 统计信息 -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">{{ total_questions|default:0 }}</div>
                        <div class="stat-label">题目总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ selected_count|default:0 }}</div>
                        <div class="stat-label">已选择</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ mc_count|default:0 }}</div>
                        <div class="stat-label">选择题</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ tf_count|default:0 }}</div>
                        <div class="stat-label">判断题</div>
                    </div>
                </div>
            </div>
            
            <!-- 操作区域 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <div class="form-check-custom">
                        <input class="form-check-input" type="checkbox" id="select-all-checkbox">
                        <label class="form-check-label" for="select-all-checkbox">
                            全选题目
                        </label>
                    </div>
                    <div>
                        <button class="action-btn" id="publish-assessment-btn" disabled>
                            <i class="fas fa-paper-plane me-2"></i>发布为考核
                        </button>
                    </div>
                </div>
                <div class="feature-card-body">
                    {% if questions %}
                        <div id="questions-container">
                            {% for question in questions %}
                                <div class="question-item" data-question-id="{{ question.id }}">
                                    <div class="question-header">
                                        <div>
                                            <div class="form-check-custom">
                                                <input class="form-check-input question-checkbox" type="checkbox" value="{{ question.id }}" id="question-{{ question.id }}">
                                                <label class="form-check-label" for="question-{{ question.id }}">
                                                    选择此题
                                                </label>
                                            </div>
                                            <div class="question-type-badge 
                                                {% if question.question_type == 'MC' %}type-mc
                                                {% elif question.question_type == 'TF' %}type-tf
                                                {% elif question.question_type == 'SA' %}type-sa
                                                {% elif question.question_type == 'PG' %}type-pg
                                                {% endif %}">
                                                {% if question.question_type == 'MC' %}选择题
                                                {% elif question.question_type == 'TF' %}判断题
                                                {% elif question.question_type == 'SA' %}简答题
                                                {% elif question.question_type == 'PG' %}编程题
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="question-actions">
                                            <button class="action-btn btn-sm-custom secondary-btn" onclick="editQuestion({{ question.id }})">
                                                <i class="fas fa-edit me-1"></i>编辑
                                            </button>
                                            <button class="action-btn btn-sm-custom secondary-btn" onclick="deleteQuestion({{ question.id }})">
                                                <i class="fas fa-trash me-1"></i>删除
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="question-text">{{ question.question_text }}</div>
                                    
                                    {% if question.options %}
                                        <div class="question-options">
                                            <strong>选项：</strong>
                                            {% for option in question.options %}
                                                <div class="option-item">{{ option }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    
                                    <div class="correct-answer">
                                        <div class="correct-answer-label">正确答案：</div>
                                        <div class="correct-answer-text">{{ question.answer }}</div>
                                    </div>
                                    
                                    <div class="question-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-book"></i>
                                            {{ question.subject|default:"未分类" }}
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            {{ question.created_at|date:"Y-m-d" }}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-question-circle"></i>
                            <h5>暂无题目</h5>
                            <p>题库中还没有题目，请先添加一些题目</p>
                            <a href="{% url 'teacher_dashboard:prepare_design' %}" class="action-btn">
                                <i class="fas fa-plus me-2"></i>创建题目
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div id="toast-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全选功能
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const questionCheckboxes = document.querySelectorAll('.question-checkbox');
        const publishBtn = document.getElementById('publish-assessment-btn');

        selectAllCheckbox.addEventListener('change', function() {
            questionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                updateQuestionSelection(checkbox);
            });
            updatePublishButton();
        });

        questionCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateQuestionSelection(this);
                updateSelectAllState();
                updatePublishButton();
            });
        });

        function updateQuestionSelection(checkbox) {
            const questionItem = checkbox.closest('.question-item');
            if (checkbox.checked) {
                questionItem.classList.add('selected');
            } else {
                questionItem.classList.remove('selected');
            }
        }

        function updateSelectAllState() {
            const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
            const totalCount = questionCheckboxes.length;
            
            selectAllCheckbox.checked = checkedCount === totalCount;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        }

        function updatePublishButton() {
            const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
            publishBtn.disabled = checkedCount === 0;
            
            if (checkedCount > 0) {
                publishBtn.innerHTML = `<i class="fas fa-paper-plane me-2"></i>发布为考核 (${checkedCount}题)`;
            } else {
                publishBtn.innerHTML = `<i class="fas fa-paper-plane me-2"></i>发布为考核`;
            }
        }

        // 发布考核
        publishBtn.addEventListener('click', function() {
            const selectedQuestions = Array.from(document.querySelectorAll('.question-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedQuestions.length === 0) {
                showToast('请至少选择一道题目', 'warning');
                return;
            }
            
            // 这里可以添加发布考核的逻辑
            showToast(`已选择 ${selectedQuestions.length} 道题目，准备发布考核`, 'success');
        });

        // 编辑题目
        function editQuestion(questionId) {
            showToast('编辑功能开发中...', 'info');
        }

        // 删除题目
        function deleteQuestion(questionId) {
            if (confirm('确定要删除这道题目吗？')) {
                showToast('删除功能开发中...', 'info');
            }
        }

        // Toast 提示
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }
    </script>
</body>
</html>

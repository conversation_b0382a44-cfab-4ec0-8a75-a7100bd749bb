<!DOCTYPE html>
<html>
<head>
    <title>考核与分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1200px;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .content-section {
            padding: 40px;
        }

        .assessment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }

        .assessment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .assessment-link {
            text-decoration: none;
            color: inherit;
            display: block;
            padding: 30px;
        }

        .assessment-link:hover {
            color: inherit;
            text-decoration: none;
        }

        .assessment-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
        }

        .assessment-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .assessment-date {
            color: #6c757d;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .assessment-stats {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .stat-item i {
            margin-right: 5px;
            color: #4f46e5;
        }

        .assessment-description {
            color: #6c757d;
            line-height: 1.5;
        }

        .empty-state {
            text-align: center;
            padding: 80px 30px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
            color: #4f46e5;
        }

        .empty-state h4 {
            color: #1e293b;
            margin-bottom: 15px;
        }

        .create-assessment-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }

        .create-assessment-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
            color: white;
            text-decoration: none;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-published {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-draft {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-tasks me-3"></i>考核与分析
            </div>
            <div class="header-subtitle">
                管理考核任务 · 分析学生表现 · 优化教学效果
            </div>
        </div>

        <div class="content-section">
            {% if assessments %}
                {% for assessment in assessments %}
                    <div class="assessment-card">
                        <a href="{% url 'teacher_dashboard:assessment_analysis' assessment.id %}" class="assessment-link">
                            <div class="assessment-meta">
                                <div class="assessment-date">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    {{ assessment.created_at|date:"Y年m月d日 H:i" }}
                                </div>
                                <div class="status-badge status-published">
                                    <i class="fas fa-check-circle me-1"></i>已发布
                                </div>
                            </div>
                            <div class="assessment-title">{{ assessment.title }}</div>
                            <div class="assessment-stats">
                                <div class="stat-item">
                                    <i class="fas fa-question-circle"></i>
                                    {{ assessment.questions.count }} 道题目
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-users"></i>
                                    参与学生数待统计
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-chart-line"></i>
                                    点击查看详细分析
                                </div>
                            </div>
                            <div class="assessment-description">
                                点击查看该考核的详细分析报告，包括学生答题情况、正确率统计、错题分析等。
                            </div>
                        </a>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h4>还没有发布任何考核</h4>
                    <p>开始创建您的第一个考核任务，了解学生的学习情况</p>
                    <a href="{% url 'teacher_dashboard:generate_assessment' %}" class="create-assessment-btn">
                        <i class="fas fa-plus me-2"></i>创建考核
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
<!DOCTYPE html>
<html>
<head>
    <title>AI赋能备课</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .content-section {
            padding: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .feature-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
        }

        .feature-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .feature-card-body {
            padding: 30px;
        }

        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
        }

        .upload-area.dragover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }

        .upload-icon {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.1rem;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .action-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
            color: white;
        }

        .action-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .secondary-btn:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .success-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .success-btn:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .ai-result-area {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 15px;
            padding: 30px;
            min-height: 300px;
            border: 1px solid #e2e8f0;
        }

        .result-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 60px 20px;
        }

        .result-placeholder i {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .teaching-plan-item {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .teaching-plan-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .plan-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .plan-meta {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .plan-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-sm-custom {
            padding: 8px 16px;
            font-size: 0.85rem;
            border-radius: 20px;
            font-weight: 500;
        }

        .question-type-selector {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .question-type-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: white;
            border-radius: 10px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
        }

        .question-type-label {
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .question-type-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: 600;
        }

        .type-mc { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
        .type-tf { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .type-sa { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .type-pg { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }

        .count-input {
            width: 80px;
            text-align: center;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 20px;
        }

        .spinner-custom {
            width: 20px;
            height: 20px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="header-title">
                <i class="fas fa-lightbulb me-3"></i>AI赋能备课
            </div>
            <div class="header-subtitle">
                智能教案生成 · 个性化题目创建 · 高效备课助手
            </div>
            <!-- 紧急清理按钮 -->
            <button id="emergency-clear-btn" onclick="clearModalBackdrop()"
                    style="position: absolute; top: 10px; right: 10px; background: rgba(255,0,0,0.8); border: 1px solid rgba(255,255,255,0.5); color: white; padding: 8px 15px; border-radius: 8px; font-size: 13px; cursor: pointer; z-index: 99999; font-weight: bold; box-shadow: 0 2px 10px rgba(0,0,0,0.3);"
                    title="如果页面变灰无法点击，请点击此按钮清除遮罩"
                    onmouseover="this.style.background='rgba(255,0,0,1)'"
                    onmouseout="this.style.background='rgba(255,0,0,0.8)'">
                🚨 清除遮罩
            </button>
        </div>

        <div class="content-section">
            <!-- 上传教学资料 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-cloud-upload-alt me-3 text-primary"></i>上传教学资料
                    </h5>
                </div>
                <div class="feature-card-body">
                    <form id="upload-form" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="upload-area" onclick="document.getElementById('teaching-document-input').click()">
                            <div class="upload-icon">
                                <i class="fas fa-file-upload"></i>
                            </div>
                            <div class="upload-text">点击上传或拖拽文件到此处</div>
                            <div class="upload-hint">支持 .txt, .pdf, .docx 格式，可同时上传多个文件</div>
                            <input class="file-input" type="file" id="teaching-document-input" name="teaching_document" multiple required accept=".txt,.pdf,.docx">
                        </div>
                        <div class="text-center mt-4">
                            <button type="submit" class="action-btn" id="generate-btn">
                                <span class="spinner-border spinner-custom me-2" role="status" aria-hidden="true" style="display: none;"></span>
                                <i class="fas fa-magic me-2"></i>
                                <span class="button-text">上传并生成教案</span>
                            </button>
                        </div>
                    </form>
                    <div id="error-message-area" class="alert alert-danger alert-custom mt-3" style="display: none;"></div>
                </div>
            </div>

            <!-- AI生成教案 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-robot me-3 text-success"></i>AI辅助生成教案
                    </h5>
                </div>
                <div class="feature-card-body">
                    <div id="ai-results-area" class="ai-result-area">
                        <div class="result-placeholder">
                            <i class="fas fa-file-alt"></i>
                            <h5>AI生成的教案将显示在这里</h5>
                            <p>上传教学资料后，AI将为您生成个性化的教案内容</p>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <div class="mb-3">
                            <small class="text-muted">第一步：处理上方AI内容</small>
                        </div>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <button type="button" id="save-plan-btn" class="action-btn success-btn">
                                <i class="fas fa-save me-2"></i>保存教案
                            </button>
                            <button type="button" id="export-btn" class="action-btn secondary-btn">
                                <i class="fas fa-download me-2"></i>导出Word文档
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 题目生成部分 -->
            <div id="generate-questions-section" class="feature-card" style="display: none;">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-cogs me-3 text-warning"></i>第二步：为已保存的教案生成题目
                    </h5>
                </div>
                <div class="feature-card-body">
                    <form id="generate-questions-form" data-plan-id="">
                        <div class="question-type-selector">
                            <h6 class="mb-3">选择题目类型和数量：</h6>
                            <div class="row">
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="question-type-item">
                                        <div class="question-type-label">
                                            <div class="question-type-icon type-mc">MC</div>
                                            <div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="MC" id="check-mc" data-question-type="MC" checked>
                                                    <label class="form-check-label" for="check-mc">选择题</label>
                                                </div>
                                                <small class="text-muted">单选或多选题目</small>
                                            </div>
                                        </div>
                                        <input type="number" class="count-input" value="3" min="1" max="10" id="count-mc" data-question-count="MC">
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="question-type-item">
                                        <div class="question-type-label">
                                            <div class="question-type-icon type-sa">SA</div>
                                            <div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="SA" id="check-sa" data-question-type="SA">
                                                    <label class="form-check-label" for="check-sa">简答题</label>
                                                </div>
                                                <small class="text-muted">开放性问答题目</small>
                                            </div>
                                        </div>
                                        <input type="number" class="count-input" value="2" min="1" max="5" id="count-sa" data-question-count="SA" disabled>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="question-type-item">
                                        <div class="question-type-label">
                                            <div class="question-type-icon type-pg">PG</div>
                                            <div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="PG" id="check-pg" data-question-type="PG">
                                                    <label class="form-check-label" for="check-pg">编程题</label>
                                                </div>
                                                <small class="text-muted">代码编写题目</small>
                                            </div>
                                        </div>
                                        <input type="number" class="count-input" value="1" min="1" max="5" id="count-pg" data-question-count="PG" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" id="generate-questions-btn" class="action-btn success-btn">
                                <span class="spinner-border spinner-custom me-2" style="display: none;" role="status" aria-hidden="true"></span>
                                <i class="fas fa-cogs me-2"></i>
                                <span class="button-text">开始生成题目</span>
                            </button>
                        </div>
                    </form>

                    <!-- 生成的题目容器 -->
                    <div id="generated-questions-container" class="mt-4"></div>
                </div>
            </div>

            <!-- 我的教案库 -->
            <div class="feature-card">
                <div class="feature-card-header">
                    <h5 class="feature-card-title">
                        <i class="fas fa-folder-open me-3 text-info"></i>我的教案库
                    </h5>
                </div>
                <div class="feature-card-body">
                    {% if teaching_plans %}
                        <div class="row">
                            {% for plan in teaching_plans %}
                            <div class="col-lg-6 col-xl-4 mb-4">
                                <div class="teaching-plan-item">
                                    <div class="plan-title">{{ plan.title }}</div>
                                    <div class="plan-meta">
                                        <i class="fas fa-calendar-alt me-2"></i>{{ plan.created_at|date:"Y年m月d日 H:i" }}
                                    </div>
                                    <div class="plan-actions">
                                        <a href="{% url 'teacher_dashboard:prepare_design' %}?plan_id={{ plan.id %}#results-section" class="action-btn btn-sm-custom">
                                            <i class="fas fa-eye me-1"></i>查看详情
                                        </a>
                                        <form method="post" action="{% url 'teacher_dashboard:generate_questions' plan.id %}" style="display: inline;">
                                            {% csrf_token %}
                                            <button type="submit" class="action-btn success-btn btn-sm-custom">
                                                <i class="fas fa-cogs me-1"></i>生成题目
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-folder-open text-muted mb-3" style="font-size: 3rem;"></i>
                                <h5>暂无教案</h5>
                                <p>上传教学资料并生成教案后，将显示在这里</p>
                            </div>
                        {% endif %}

        {% if selected_plan %}
        <!-- Generated Questions Card -->
        <div class="card shadow mb-4" id="results-section" {% if not selected_plan %}style="display: none;"{% endif %}>
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary plan-title">教案详情与生成结果：{{ selected_plan.title|default:"待选择" }}</h6>
            </div>
            <div class="card-body">
                <h5>教案内容</h5>
                <div class="p-3 bg-light rounded mb-3">
                    {{ selected_plan.content|linebreaksbr|default:"请先在上方生成或在下方选择一个教案。" }}
                </div>
                
                <hr>
                
                <h5>基于该教案生成的题目</h5>
                <div id="existing-questions-list">
                    {% if generated_questions %}
                        {% for question in generated_questions %}
                            <div class="card mb-2">
                                <div class="card-body">
                                    <strong>{{ question.get_question_type_display }}:</strong> {{ question.question_text }}
                                    <br>
                                    <small><strong>答案:</strong> {{ question.correct_answer }}</small>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">此教案下暂无题目。</p>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 保存教案模态框 -->
    <div class="modal fade" id="savePlanModal" tabindex="-1" aria-labelledby="savePlanModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                    <h5 class="modal-title" id="savePlanModalLabel">
                        <i class="fas fa-save me-2"></i>保存教案
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <form id="save-plan-form">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="plan-title" class="form-label">教案标题</label>
                            <input type="text" class="form-control" id="plan-title" name="title" required
                                   style="border-radius: 10px; padding: 12px; border: 1px solid #e2e8f0;">
                        </div>
                        <input type="hidden" id="plan-content-hidden" name="content">
                    </form>
                </div>
                <div class="modal-footer" style="border-top: 1px solid #f1f5f9; border-radius: 0 0 20px 20px;">
                    <button type="button" class="action-btn secondary-btn" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="button" class="action-btn success-btn" id="confirm-save-btn">
                        <i class="fas fa-check me-2"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
    let generatedPlanContent = '';

    // 页面加载时清理可能残留的模态框背景
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，开始初始化');

        // 清理背景遮罩
        setTimeout(clearModalBackdrop, 100);

        // 初始化按钮事件
        setTimeout(function() {
            console.log('初始化按钮事件');
            bindSavePlanButton();
            bindExportButton();
        }, 200);

        // 设置定期检查和清理
        setInterval(function() {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 0) {
                console.log('检测到背景遮罩，自动清理');
                clearModalBackdrop();
            }
        }, 1000); // 每秒检查一次
    });

    // 添加键盘快捷键 Esc 来清除背景遮罩
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            console.log('按下Esc键，清除背景遮罩');
            clearModalBackdrop();
        }
    });

    // 监听点击事件，如果点击到背景遮罩也清除
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal-backdrop')) {
            console.log('点击背景遮罩，自动清除');
            clearModalBackdrop();
        }
    });

    // 绑定保存教案按钮事件的函数
    function bindSavePlanButton() {
        // 获取所有保存教案按钮（可能有多个）
        const savePlanBtns = document.querySelectorAll('#save-plan-btn, [id="save-plan-btn"]');
        console.log('找到保存教案按钮数量:', savePlanBtns.length);

        savePlanBtns.forEach((btn, index) => {
            console.log(`绑定保存教案按钮 ${index + 1}`);
            // 移除之前的事件监听器
            btn.removeEventListener('click', showSaveModal);
            // 添加新的事件监听器
            btn.addEventListener('click', showSaveModal);
        });
    }

    // 强制清除所有可能的背景遮罩的函数
    function clearModalBackdrop() {
        console.log('开始清除背景遮罩...');

        // 移除所有模态框背景遮罩
        const backdrops = document.querySelectorAll('.modal-backdrop');
        console.log('找到背景遮罩数量:', backdrops.length);
        backdrops.forEach((backdrop, index) => {
            console.log(`移除背景遮罩 ${index + 1}`);
            backdrop.remove();
        });

        // 移除所有可能的遮罩层
        const overlays = document.querySelectorAll('[class*="overlay"], [class*="backdrop"], [style*="position: fixed"], [style*="z-index"]');
        overlays.forEach((overlay, index) => {
            const style = window.getComputedStyle(overlay);
            if (style.position === 'fixed' &&
                (style.backgroundColor.includes('rgba') || style.opacity < 1) &&
                parseInt(style.zIndex) > 1000) {
                console.log(`移除可疑遮罩层 ${index + 1}:`, overlay);
                overlay.remove();
            }
        });

        // 移除body上的modal相关类
        document.body.classList.remove('modal-open');
        document.body.classList.remove('modal-backdrop');

        // 恢复body的样式
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        document.body.style.position = '';

        // 检查并移除任何固定定位的灰色元素
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            const style = window.getComputedStyle(element);
            if (style.position === 'fixed' &&
                style.top === '0px' &&
                style.left === '0px' &&
                style.width === '100%' &&
                style.height === '100%' &&
                (style.backgroundColor.includes('rgba') || parseFloat(style.opacity) < 1)) {
                console.log('移除全屏遮罩元素:', element);
                element.remove();
            }
        });

        console.log('背景遮罩清除完成');
    }

    // 显示保存模态框的函数
    function showSaveModal() {
        console.log('显示保存模态框');

        // 先清除可能存在的背景遮罩
        clearModalBackdrop();

        const modalElement = document.getElementById('savePlanModal');
        if (!modalElement) {
            console.error('找不到模态框元素');
            return;
        }

        const modal = new bootstrap.Modal(modalElement);
        // 清空之前的输入
        const titleInput = document.getElementById('plan-title');
        if (titleInput) {
            titleInput.value = '';
        }
        modal.show();
    }

    // 文件上传拖拽功能
    const uploadArea = document.querySelector('.upload-area');
    const fileInput = document.getElementById('teaching-document-input');

    if (uploadArea && fileInput) {
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
            updateFileDisplay();
        });

        fileInput.addEventListener('change', updateFileDisplay);
    }

    function updateFileDisplay() {
        const files = fileInput.files;
        if (files.length > 0) {
            const fileNames = Array.from(files).map(file => file.name).join(', ');
            uploadArea.querySelector('.upload-text').textContent = `已选择 ${files.length} 个文件`;
            uploadArea.querySelector('.upload-hint').textContent = fileNames;
        }
    }

    // 表单提交处理
    const teachingForm = document.getElementById('upload-form');
    if (teachingForm) {
        teachingForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const files = fileInput.files;
            if (files.length === 0) {
                alert('请先选择文件');
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('teaching_document', files[i]);
            }

            // 添加CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            formData.append('csrfmiddlewaretoken', csrfToken);

            const generateBtn = document.getElementById('generate-btn');
            const spinner = generateBtn.querySelector('.spinner-border');
            const btnText = generateBtn.querySelector('.button-text');
            const resultArea = document.getElementById('ai-results-area');
            const placeholder = resultArea.querySelector('.result-placeholder');
            const generatedContent = document.getElementById('generated-content');

            // 显示加载状态
            spinner.style.display = 'inline-block';
            btnText.textContent = '生成中...';
            generateBtn.disabled = true;

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    generatedPlanContent = data.preparation_suggestions;
                    resultArea.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2 text-success"></i>AI生成的教案
                            </h5>
                            <div class="d-flex gap-2">
                                <button class="action-btn success-btn" id="save-plan-btn">
                                    <i class="fas fa-save me-2"></i>保存教案
                                </button>
                                <button class="action-btn secondary-btn" id="export-btn">
                                    <i class="fas fa-download me-2"></i>导出Word
                                </button>
                            </div>
                        </div>
                        <div id="plan-content" class="border rounded p-3" style="background: white; max-height: 500px; overflow-y: auto;">${marked.parse(generatedPlanContent)}</div>
                    `;

                    // 重新绑定按钮事件
                    bindSavePlanButton();
                    bindExportButton();
                } else {
                    alert('生成失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('生成失败，请重试');
                document.getElementById('error-message-area').textContent = '生成失败: ' + error;
                document.getElementById('error-message-area').style.display = 'block';
            })
            .finally(() => {
                spinner.style.display = 'none';
                btnText.textContent = '上传并生成教案';
                generateBtn.disabled = false;
            });
        });
    }

    // 绑定导出Word按钮事件的函数
    function bindExportButton() {
        // 获取所有导出按钮（可能有多个）
        const exportBtns = document.querySelectorAll('#export-btn, [id="export-btn"]');
        console.log('找到导出按钮数量:', exportBtns.length);

        exportBtns.forEach((btn, index) => {
            console.log(`绑定导出按钮 ${index + 1}`);
            // 移除之前的事件监听器
            btn.removeEventListener('click', exportToWord);
            // 添加新的事件监听器
            btn.addEventListener('click', exportToWord);
        });
    }

    // 导出Word文档的函数
    function exportToWord() {
        console.log('点击导出Word按钮');

        if (!generatedPlanContent) {
            alert('请先生成教案内容再导出');
            return;
        }

        try {
            // 获取CSRF令牌
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            console.log('准备导出Word文档');

            // 创建表单数据
            const formData = new FormData();
            formData.append('title', '教案_' + new Date().toISOString().slice(0, 10));
            formData.append('content', generatedPlanContent);
            formData.append('csrfmiddlewaretoken', csrfToken);

            // 显示加载状态
            const exportBtns = document.querySelectorAll('#export-btn');
            exportBtns.forEach(btn => {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>导出中...';
            });

            // 发送请求到后端生成Word文档
            fetch('/teacher-dashboard/api/export-lesson-plan/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('导出失败: ' + response.status);
                }
                return response.blob();
            })
            .then(blob => {
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `AI教案_${new Date().toISOString().slice(0, 10)}.docx`;

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 释放URL对象
                window.URL.revokeObjectURL(url);

                console.log('Word文档导出成功');
                alert('教案已成功导出为Word文档！');
            })
            .catch(error => {
                console.error('导出失败:', error);
                alert('导出失败: ' + error.message);
            })
            .finally(() => {
                // 恢复按钮状态
                exportBtns.forEach(btn => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-download me-2"></i>导出Word';
                });
            });

        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败，请重试');
        }
    }

    // 初始化按钮
    bindSavePlanButton();
    bindExportButton();

    const confirmSaveBtn = document.getElementById('confirm-save-btn');
    if (confirmSaveBtn) {
        console.log('找到确认保存按钮，添加事件监听器');
        confirmSaveBtn.addEventListener('click', function() {
            console.log('点击确认保存按钮');
            const title = document.getElementById('plan-title').value;
            if (!title.trim()) {
                alert('请输入教案标题');
                return;
            }

            console.log('准备保存教案，标题:', title);
            console.log('教案内容长度:', generatedPlanContent.length);

            // 禁用按钮防止重复点击
            confirmSaveBtn.disabled = true;
            confirmSaveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';

            // 获取CSRF令牌
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            console.log('CSRF令牌:', csrfToken ? '已获取' : '未找到');

            // 使用JSON格式发送数据
            const requestData = {
                title: title,
                content: generatedPlanContent
            };

            console.log('发送保存请求到服务器');

            fetch('/teacher-dashboard/api/save-lesson-plan/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: new URLSearchParams({
                    'title': title,
                    'content': generatedPlanContent,
                    'csrfmiddlewaretoken': csrfToken
                })
            })
            .then(response => {
                console.log('收到服务器响应:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('解析响应数据:', data);
                if (data.status === 'success') {
                    console.log('保存成功，准备关闭模态框');

                    try {
                        // 关闭模态框
                        const modalElement = document.getElementById('savePlanModal');
                        const modal = bootstrap.Modal.getInstance(modalElement);
                        if (modal) {
                            modal.hide();
                            console.log('使用Bootstrap API关闭模态框');
                        } else {
                            console.warn('无法获取模态框实例，使用手动方式关闭');
                        }

                        // 无论如何都强制清除背景遮罩
                        setTimeout(() => {
                            clearModalBackdrop();
                        }, 100);

                    } catch (modalError) {
                        console.error('关闭模态框时出错:', modalError);
                        // 出错时强制清除背景遮罩
                        clearModalBackdrop();
                    }

                    alert('教案保存成功！');
                    console.log('刷新页面');
                    location.reload();
                } else {
                    console.error('保存失败:', data.message);
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('请求错误:', error);
                alert('保存失败，请重试: ' + error.message);
            })
            .finally(() => {
                console.log('请求完成，恢复按钮状态');
                // 恢复按钮状态
                confirmSaveBtn.disabled = false;
                confirmSaveBtn.innerHTML = '<i class="fas fa-check me-2"></i>保存';
            });
        });
    }

    // 题目类型选择器
    document.querySelectorAll('[data-question-type]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const countInput = document.getElementById('count-' + this.value.toLowerCase());
            if (countInput) {
                countInput.disabled = !this.checked;
                if (!this.checked) {
                    countInput.value = 1;
                }
            }
        });
    });
</script>
</body>
</html>